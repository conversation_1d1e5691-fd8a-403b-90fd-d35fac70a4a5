# API 参考文档

## 收入调整服务 (IncomeAdjustmentService)

### 构造函数
```javascript
const incomeAdjustmentService = new IncomeAdjustmentService()
```

### 添加操作

#### addExtraIncome(date, type, amount, description)
添加额外收入记录

**参数**：
- `date` (Date|string): 日期
- `type` (string): 收入类型（用户自定义）
- `amount` (number): 金额
- `description` (string): 描述（可选）

**返回值**：
- `number`: 新创建记录的ID

**示例**：
```javascript
const id = incomeAdjustmentService.addExtraIncome(
  new Date(), 
  "销售提成", 
  100.50, 
  "完成大单"
)
```

#### addDeduction(date, type, amount, description)
添加扣款记录

**参数**：
- `date` (Date|string): 日期
- `type` (string): 扣款类型（用户自定义）
- `amount` (number): 金额
- `description` (string): 描述（可选）

**返回值**：
- `number`: 新创建记录的ID

**示例**：
```javascript
const id = incomeAdjustmentService.addDeduction(
  new Date(), 
  "迟到扣款", 
  12.34, 
  "堵车迟到了"
)
```

### 编辑操作

#### editExtraIncome(date, originalItem, type, amount, description)
编辑额外收入记录

**参数**：
- `date` (Date|string): 日期
- `originalItem` (Object): 原始记录对象
- `type` (string): 新的收入类型
- `amount` (number): 新的金额
- `description` (string): 新的描述

**返回值**：
- `boolean`: 编辑是否成功

**示例**：
```javascript
const success = incomeAdjustmentService.editExtraIncome(
  new Date(),
  originalItem,
  "绩效奖金",
  150.00,
  "月度绩效"
)
```

#### editDeduction(date, originalItem, type, amount, description)
编辑扣款记录

**参数**：
- `date` (Date|string): 日期
- `originalItem` (Object): 原始记录对象
- `type` (string): 新的扣款类型
- `amount` (number): 新的金额
- `description` (string): 新的描述

**返回值**：
- `boolean`: 编辑是否成功

### 删除操作

#### deleteExtraIncome(date, itemId)
删除额外收入记录

**参数**：
- `date` (Date|string): 日期
- `itemId` (number): 记录ID

**返回值**：
- `boolean`: 删除是否成功

#### deleteDeduction(date, itemId)
删除扣款记录

**参数**：
- `date` (Date|string): 日期
- `itemId` (number): 记录ID

**返回值**：
- `boolean`: 删除是否成功

### 查询操作

#### getDayAdjustmentSummary(date, workId)
获取指定日期的收入调整汇总

**参数**：
- `date` (Date|string): 日期
- `workId` (string): 工作履历ID（可选）

**返回值**：
```javascript
{
  extraIncome: number,        // 额外收入总额
  deductions: number,         // 扣款总额
  netAdjustment: number,      // 净调整金额
  extraIncomeItems: Array,    // 额外收入明细
  deductionItems: Array       // 扣款明细
}
```

#### getAdjustmentStatistics(workId, dateRange)
获取收入调整统计数据

**参数**：
- `workId` (string): 工作履历ID
- `dateRange` (Object): 日期范围 `{start: Date, end: Date}`

**返回值**：
```javascript
{
  totalExtraIncome: number,
  totalDeductions: number,
  netAdjustment: number,
  extraIncomeByType: Object,
  deductionsByType: Object
}
```

## 时间跟踪管理器 (TimeTrackingManager)

### 动态计算方法

#### calculateTotalWorkMinutes(dayData)
计算总工作时长

**参数**：
- `dayData` (Object): 日期数据

**返回值**：
- `number`: 总工作时长（分钟）

#### calculateDailyIncome(dayData)
计算基础收入

**参数**：
- `dayData` (Object): 日期数据

**返回值**：
- `number`: 基础收入

#### calculateTotalExtraIncome(extraIncomes)
计算额外收入总额

**参数**：
- `extraIncomes` (Array): 额外收入数组

**返回值**：
- `number`: 额外收入总额

#### calculateTotalDeductions(deductions)
计算扣款总额

**参数**：
- `deductions` (Array): 扣款数组

**返回值**：
- `number`: 扣款总额

#### calculateNetIncome(dayData)
计算净收入

**参数**：
- `dayData` (Object): 日期数据

**返回值**：
- `number`: 净收入

#### getDayDataStats(dayData)
获取完整统计信息

**参数**：
- `dayData` (Object): 日期数据

**返回值**：
```javascript
{
  totalWorkMinutes: number,
  dailyIncome: number,
  hourlyRate: number,
  extraIncome: number,
  deductions: number,
  netIncome: number
}
```

#### generateNextId(existingItems)
生成下一个可用ID

**参数**：
- `existingItems` (Array): 现有项目数组

**返回值**：
- `number`: 下一个可用ID

## 统计服务 (StatisticsService)

### 缓存管理

#### clearStatisticsCache()
清除统计缓存

**返回值**：
- `void`

### 统计计算

#### calculateOverviewStatistics(workId, dateRange)
计算概览统计

**参数**：
- `workId` (string): 工作履历ID
- `dateRange` (Object): 日期范围

**返回值**：
```javascript
{
  totalWorkDays: number,
  totalWorkMinutes: number,
  totalIncome: number,
  averageHourlyRate: number,
  totalExtraIncome: number,
  totalDeductions: number
}
```

## 收入调整模态框组件

### 属性 (Properties)

#### show
- **类型**: Boolean
- **默认值**: false
- **描述**: 控制模态框显示/隐藏

#### mode
- **类型**: String
- **默认值**: 'income'
- **可选值**: 'income' | 'deduction'
- **描述**: 模态框模式

#### isEdit
- **类型**: Boolean
- **默认值**: false
- **描述**: 是否为编辑模式

#### editItem
- **类型**: Object
- **默认值**: null
- **描述**: 编辑的项目数据

### 方法 (Methods)

#### show(options)
显示模态框

**参数**：
```javascript
{
  mode: 'income' | 'deduction',
  isEdit: boolean,
  editItem: Object
}
```

#### hide()
隐藏模态框

### 事件 (Events)

#### success
保存成功事件

**事件数据**：
```javascript
{
  mode: string,
  type: string,
  amount: number,
  description: string,
  id: number,
  isEdit: boolean,
  editItem: Object
}
```

#### cancel
取消事件

## 数据结构

### 收入调整项目
```javascript
{
  id: number,           // 项目ID
  type: string,         // 类型（用户自定义）
  desc: string,         // 描述（可空）
  amount: number        // 金额
}
```

### 日期数据结构
```javascript
{
  workDate: Date,       // 工作日期
  status: string,       // 日期状态
  segments: Array,      // 时间段数组
  fishes: Array,        // 摸鱼数据数组
  extraIncomes: Array,  // 额外收入数组
  deductions: Array,    // 扣款数组
  createTime: Date      // 创建时间
}
```

## 错误处理

### 常见错误类型

#### 数据验证错误
```javascript
{
  type: 'VALIDATION_ERROR',
  message: '数据验证失败',
  errors: ['类型不能为空', '金额必须大于0']
}
```

#### 数据不存在错误
```javascript
{
  type: 'NOT_FOUND_ERROR',
  message: '记录不存在',
  itemId: 123
}
```

#### 操作失败错误
```javascript
{
  type: 'OPERATION_ERROR',
  message: '操作失败',
  operation: 'add|edit|delete'
}
```

## 最佳实践

### 1. 错误处理
```javascript
try {
  const id = incomeAdjustmentService.addExtraIncome(date, type, amount, desc)
  console.log('添加成功，ID:', id)
} catch (error) {
  console.error('添加失败:', error.message)
  wx.showToast({
    title: '添加失败',
    icon: 'error'
  })
}
```

### 2. 数据验证
```javascript
// 在调用API前进行基础验证
if (!type || type.trim() === '') {
  throw new Error('类型不能为空')
}

if (!amount || amount <= 0) {
  throw new Error('金额必须大于0')
}
```

### 3. 缓存管理
```javascript
// 数据变更后清除缓存
incomeAdjustmentService.addExtraIncome(...)
statisticsService.clearStatisticsCache()
```
