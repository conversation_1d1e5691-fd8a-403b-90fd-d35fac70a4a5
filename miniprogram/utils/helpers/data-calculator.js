/**
 * 数据计算工具
 * 提供各种数据计算方法
 */

class DataCalculator {
  /**
   * 计算两个日期之间的天数
   * @param {Date|string} startDate - 开始日期
   * @param {Date|string} endDate - 结束日期
   * @returns {number} 天数差
   */
  static daysBetween(startDate, endDate) {
    if (!startDate || !endDate) return 0
    
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0
    
    const diffTime = Math.abs(end.getTime() - start.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  /**
   * 计算工作日天数（排除周末）
   * @param {Date|string} startDate - 开始日期
   * @param {Date|string} endDate - 结束日期
   * @returns {number} 工作日天数
   */
  static workdaysBetween(startDate, endDate) {
    if (!startDate || !endDate) return 0
    
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0
    
    let workdays = 0
    const current = new Date(start)
    
    while (current <= end) {
      const dayOfWeek = current.getDay()
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // 不是周日(0)和周六(6)
        workdays++
      }
      current.setDate(current.getDate() + 1)
    }
    
    return workdays
  }

  /**
   * 计算平均值
   * @param {number[]} numbers - 数字数组
   * @returns {number} 平均值
   */
  static average(numbers) {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }

    const validNumbers = numbers.filter(num => !isNaN(Number(num)))
    if (validNumbers.length === 0) {
      return 0
    }

    const sum = validNumbers.reduce((acc, num) => acc + Number(num), 0)
    return sum / validNumbers.length
  }

  /**
   * 计算中位数
   * @param {number[]} numbers - 数字数组
   * @returns {number} 中位数
   */
  static median(numbers) {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }

    const validNumbers = numbers.filter(num => !isNaN(Number(num))).map(num => Number(num))
    if (validNumbers.length === 0) {
      return 0
    }

    const sorted = validNumbers.sort((a, b) => a - b)
    const middle = Math.floor(sorted.length / 2)

    if (sorted.length % 2 === 0) {
      return (sorted[middle - 1] + sorted[middle]) / 2
    } else {
      return sorted[middle]
    }
  }

  /**
   * 计算标准差
   * @param {number[]} numbers - 数字数组
   * @returns {number} 标准差
   */
  static standardDeviation(numbers) {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }

    const validNumbers = numbers.filter(num => !isNaN(Number(num))).map(num => Number(num))
    if (validNumbers.length === 0) {
      return 0
    }

    const avg = this.average(validNumbers)
    const squaredDiffs = validNumbers.map(num => Math.pow(num - avg, 2))
    const avgSquaredDiff = this.average(squaredDiffs)
    
    return Math.sqrt(avgSquaredDiff)
  }

  /**
   * 计算百分比
   * @param {number} part - 部分值
   * @param {number} total - 总值
   * @param {number} decimals - 小数位数
   * @returns {number} 百分比
   */
  static percentage(part, total, decimals = 2) {
    if (isNaN(Number(part)) || isNaN(Number(total)) || Number(total) === 0) {
      return 0
    }

    const percentage = (Number(part) / Number(total)) * 100
    return Number(percentage.toFixed(decimals))
  }

  /**
   * 计算增长率
   * @param {number} oldValue - 旧值
   * @param {number} newValue - 新值
   * @param {number} decimals - 小数位数
   * @returns {number} 增长率百分比
   */
  static growthRate(oldValue, newValue, decimals = 2) {
    if (isNaN(Number(oldValue)) || isNaN(Number(newValue)) || Number(oldValue) === 0) {
      return 0
    }

    const rate = ((Number(newValue) - Number(oldValue)) / Number(oldValue)) * 100
    return Number(rate.toFixed(decimals))
  }

  /**
   * 计算复合年增长率 (CAGR)
   * @param {number} beginningValue - 初始值
   * @param {number} endingValue - 结束值
   * @param {number} years - 年数
   * @param {number} decimals - 小数位数
   * @returns {number} 复合年增长率
   */
  static compoundAnnualGrowthRate(beginningValue, endingValue, years, decimals = 2) {
    if (isNaN(Number(beginningValue)) || isNaN(Number(endingValue)) || 
        isNaN(Number(years)) || Number(beginningValue) === 0 || Number(years) === 0) {
      return 0
    }

    const cagr = (Math.pow(Number(endingValue) / Number(beginningValue), 1 / Number(years)) - 1) * 100
    return Number(cagr.toFixed(decimals))
  }

  /**
   * 计算最大值
   * @param {number[]} numbers - 数字数组
   * @returns {number} 最大值
   */
  static max(numbers) {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }

    const validNumbers = numbers.filter(num => !isNaN(Number(num))).map(num => Number(num))
    if (validNumbers.length === 0) {
      return 0
    }

    return Math.max(...validNumbers)
  }

  /**
   * 计算最小值
   * @param {number[]} numbers - 数字数组
   * @returns {number} 最小值
   */
  static min(numbers) {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }

    const validNumbers = numbers.filter(num => !isNaN(Number(num))).map(num => Number(num))
    if (validNumbers.length === 0) {
      return 0
    }

    return Math.min(...validNumbers)
  }

  /**
   * 计算总和
   * @param {number[]} numbers - 数字数组
   * @returns {number} 总和
   */
  static sum(numbers) {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }

    const validNumbers = numbers.filter(num => !isNaN(Number(num))).map(num => Number(num))
    return validNumbers.reduce((acc, num) => acc + num, 0)
  }

  /**
   * 计算方差
   * @param {number[]} numbers - 数字数组
   * @returns {number} 方差
   */
  static variance(numbers) {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }

    const validNumbers = numbers.filter(num => !isNaN(Number(num))).map(num => Number(num))
    if (validNumbers.length === 0) {
      return 0
    }

    const avg = this.average(validNumbers)
    const squaredDiffs = validNumbers.map(num => Math.pow(num - avg, 2))
    
    return this.average(squaredDiffs)
  }

  /**
   * 计算年龄
   * @param {Date|string} birthDate - 出生日期
   * @param {Date|string} currentDate - 当前日期（可选）
   * @returns {number} 年龄
   */
  static calculateAge(birthDate, currentDate = new Date()) {
    if (!birthDate) return 0
    
    const birth = new Date(birthDate)
    const current = new Date(currentDate)
    
    if (isNaN(birth.getTime()) || isNaN(current.getTime())) return 0
    
    let age = current.getFullYear() - birth.getFullYear()
    const monthDiff = current.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && current.getDate() < birth.getDate())) {
      age--
    }
    
    return Math.max(0, age)
  }

  /**
   * 计算BMI指数
   * @param {number} weight - 体重（公斤）
   * @param {number} height - 身高（米）
   * @param {number} decimals - 小数位数
   * @returns {number} BMI指数
   */
  static calculateBMI(weight, height, decimals = 1) {
    if (isNaN(Number(weight)) || isNaN(Number(height)) || Number(height) === 0) {
      return 0
    }

    const bmi = Number(weight) / Math.pow(Number(height), 2)
    return Number(bmi.toFixed(decimals))
  }

  /**
   * 从摸鱼历史记录中统计快捷备注
   * @param {Object} userData - 用户数据
   * @param {number} limit - 返回的最大数量，默认10
   * @returns {Array} 快捷备注列表 [{ remark, count }]
   */
  static getQuickRemarksFromHistory(userData, limit = 10) {
    const remarkFrequency = new Map()

    try {
      // 遍历所有工作履历
      Object.values(userData.workHistory || {}).forEach(work => {
        // 遍历所有日期的摸鱼记录
        Object.values(work.timeTracking || {}).forEach(dayData => {
          if (dayData.fishes && Array.isArray(dayData.fishes)) {
            dayData.fishes.forEach(fish => {
              if (fish.remark && fish.remark.trim()) {
                const remark = fish.remark.trim()
                // 过滤掉自动结束的备注
                if (!remark.includes('(自动结束)')) {
                  remarkFrequency.set(remark, (remarkFrequency.get(remark) || 0) + 1)
                }
              }
            })
          }
        })
      })

      // 按频次排序，取前N个
      return Array.from(remarkFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, limit)
        .map(([remark, count]) => ({ remark, count }))
    } catch (error) {
      console.error('统计快捷备注失败:', error)
      return []
    }
  }
}

module.exports = {
  DataCalculator
}
