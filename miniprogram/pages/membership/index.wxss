/* 会员页面样式 */
.membership-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f4fff7 0%, #d8e8f8 100%);
  padding: 20rpx;
}

/* 会员状态卡片 */
.membership-status-card {
  background: #1a1a1a;
  border-radius: 32rpx;
  margin-bottom: 32rpx;
  padding: 40rpx;
  position: relative;
  overflow: hidden;
}

.membership-status-card.vip {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
}

.membership-status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.35), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.status-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.status-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.status-icon-container {
  position: relative;
  margin-right: 24rpx;
}

.crown-icon {
  font-size: 64rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(255, 215, 0, 0.3));
}

.level-badge {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 600;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.level-text {
  font-size: 18rpx;
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-title {
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 8rpx;
  letter-spacing: 1rpx;
}

.membership-status-card.vip .status-title {
  color: #8b4513;
}

.status-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  margin-bottom: 8rpx;
}

.membership-status-card.vip .status-subtitle {
  color: rgba(139, 69, 19, 0.8);
}

.status-stats {
  display: flex;
  gap: 16rpx;
}

.stat-item {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.membership-status-card.vip .stat-item {
  color: rgba(139, 69, 19, 0.6);
  background: rgba(139, 69, 19, 0.1);
}

.upgrade-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.upgrade-badge:active {
  transform: scale(0.95);
}

/* 区域标题 */
.section-header {
  margin-bottom: 24rpx;
  text-align: center;
}

.section-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  display: block;
  margin-bottom: 8rpx;
}

.section-subtitle {
  font-size: 26rpx;
  color: #666;
  font-weight: 400;
}

/* 使用统计区域 */
.usage-stats-section {
  margin-bottom: 32rpx;
}

.stats-grid {
  display: flex;
  gap: 16rpx;
}

.stat-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.stat-icon {
  font-size: 36rpx;
  margin-bottom: 12rpx;
}

.stat-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 4rpx;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

/* 会员权益区域 */
.benefits-section {
  margin-bottom: 40rpx;
}

.benefits-comparison {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.comparison-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
}

.comparison-row {
  display: flex;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.comparison-row:last-child {
  border-bottom: none;
}

.comparison-item {
  flex: 1;
  padding: 24rpx 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.comparison-item:not(:last-child) {
  border-right: 1rpx solid rgba(0, 0, 0, 0.05);
}

.comparison-title {
  font-size: 28rpx;
  font-weight: 700;
  color: #1a1a1a;
}

.feature-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.feature-icon {
  font-size: 32rpx;
}

.feature-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.feature-limit {
  font-size: 24rpx;
  color: #dc3545;
  font-weight: 600;
}

.feature-unlimited {
  font-size: 24rpx;
  color: #28a745;
  font-weight: 600;
}

/* 专属功能区域 */
.exclusive-features-section {
  margin-bottom: 40rpx;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.feature-card:active {
  transform: scale(0.95);
}

.feature-card .feature-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.feature-card .feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.feature-card .feature-desc {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}



/* 获取VIP区域 */
.get-vip-section {
  margin-bottom: 40rpx;
}

.get-vip-options {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.vip-option {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.vip-option:last-child {
  border-bottom: none;
}

.vip-option:active {
  background: rgba(0, 0, 0, 0.05);
}

.option-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.option-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.option-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}

.option-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.option-arrow {
  font-size: 32rpx;
  color: #999;
  margin-left: 16rpx;
}

/* VIP记录区域 */
.vip-records-section {
  margin-bottom: 40rpx;
}

.records-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
  padding: 32rpx;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.records-card:active {
  transform: scale(0.98);
}

.records-summary {
  flex: 1;
  display: flex;
  gap: 40rpx;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.summary-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.records-arrow {
  font-size: 32rpx;
  color: #999;
  margin-left: 16rpx;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 24rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  cursor: pointer;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

.modal-btn {
  padding: 16rpx 32rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
}

.modal-btn.secondary:active {
  background: #e9ecef;
}

/* VIP记录统计 */
.records-stats {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.stats-item {
  flex: 1;
  text-align: center;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.stats-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* VIP记录列表 */
.records-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.empty-records {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.record-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.record-days {
  font-size: 32rpx;
  font-weight: 700;
  color: #28a745;
}

.record-date {
  font-size: 24rpx;
  color: #999;
}

.record-content {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.record-source {
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 600;
}

.record-desc {
  font-size: 24rpx;
  color: #666;
}

/* 推荐奖励区域 */
.invite-section {
  margin-bottom: 40rpx;
}

.invite-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
}

.invite-card:active {
  transform: scale(0.98);
}

.invite-content {
  display: flex;
  align-items: center;
  padding: 32rpx;
}

.invite-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.invite-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.invite-info {
  display: flex;
  flex-direction: column;
}

.invite-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.invite-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.invite-right {
  margin-left: 24rpx;
}

.invite-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

/* 反馈区域 */
.feedback-section {
  margin-bottom: 40rpx;
}

.feedback-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
}

.feedback-card:active {
  transform: scale(0.98);
}

.feedback-content {
  display: flex;
  align-items: center;
  padding: 32rpx;
}

.feedback-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.feedback-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.feedback-info {
  display: flex;
  flex-direction: column;
}

.feedback-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.feedback-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.feedback-right {
  margin-left: 24rpx;
}

.feedback-arrow {
  font-size: 32rpx;
  color: #999;
}

/* 邀请模态框样式 */
.invite-modal-content {
  text-align: center;
}

.invite-reward-info {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.reward-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  display: block;
}

.reward-desc {
  font-size: 26rpx;
  color: #666;
}

.invite-code-section {
  margin-bottom: 32rpx;
}

.code-label {
  font-size: 28rpx;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  display: block;
}

.code-container {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 16rpx;
  gap: 16rpx;
}

.invite-code-text {
  flex: 1;
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-align: center;
}

.copy-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.copy-btn:active {
  transform: scale(0.95);
}

.invite-actions {
  display: flex;
  gap: 16rpx;
  justify-content: center;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  min-width: 120rpx;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 反馈表单样式 */
.feedback-form {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
}

.feedback-types {
  display: flex;
  gap: 12rpx;
}

.type-option {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.type-option.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
}

.type-option:active {
  transform: scale(0.95);
}

.feedback-textarea {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #1a1a1a;
  min-height: 200rpx;
  resize: none;
}

.feedback-textarea:focus {
  border-color: #667eea;
  outline: none;
}

.char-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 8rpx;
}

.feedback-input {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #1a1a1a;
}

.feedback-input:focus {
  border-color: #667eea;
  outline: none;
}

.modal-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.modal-btn.primary:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
