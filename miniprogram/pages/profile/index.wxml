<!-- 个人页面 -->
<view class="profile-container">
  <!-- 用户信息区域 -->
  <view class="user-info-section" bindtap="onEditUserInfo">
    <view class="user-header">
      <view class="user-avatar">
        <image wx:if="{{userInfo.avatar}}" src="{{userInfo.avatar}}" class="avatar-image" />
        <view wx:else class="avatar-placeholder">{{userInfo.nickname.charAt(0)}}</view>
      </view>

      <view class="user-details">
        <view class="user-nickname">{{userInfo.nickname || '未设置昵称'}}</view>
        <view class="user-subtitle">ID: {{userInfo.no || '?'}}</view>
      </view>

      <!-- 签到按钮 -->
      <view class="check-in-btn-container">
        <view wx:if="{{checkInStatus.loading}}">
          <view class="check-in-btn loading">
            <text class="btn-text">加载中...</text>
          </view>
        </view>
        <view wx:elif="{{!checkInStatus.hasCheckedInToday}}" catchtap="onQuickCheckIn">
          <view class="check-in-btn">
            <text class="btn-text">今日未签到</text>
          </view>
        </view>
        <view wx:else catchtap="onGoToCheckIn">
          <view class="check-in-btn checked">
            <text class="btn-text">已连续签到 {{checkInStatus.consecutiveDays}} 天</text>
            <view class="setting-arrow">›</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 用户统计区域 -->
  <view class="user-stats-section">
    <view class="stats-grid">
      <view class="stat-item" bindtap="onShowRegistrationDays">
        <view class="stat-label">已加入</view>
        <view class="stat-value">{{userStats.registrationDays || 0}}</view>
        <view class="stat-label">天</view>
      </view>
      <view class="stat-item" bindtap="onViewRedemptionCodes">
        <view class="stat-label">兑换码</view>
        <view class="stat-value">{{userStats.redemptionCodes || 0}}</view>
        <view class="setting-arrow">›</view>
      </view>
      <view class="stat-item" bindtap="onViewPoints">
        <view class="stat-label">积分</view>
        <view class="stat-value">{{userInfo.points || 0}}</view>
        <view class="setting-arrow">›</view>
      </view>
    </view>
  </view>

  <!-- 会员信息区域 -->
  <view class="membership-card {{userInfo.vip.status ? 'vip' : ''}}" bindtap="onViewMembershipBenefits">
    <view class="membership-content">
      <view class="membership-left">
        <text class="crown-icon">👑</text>
        <view class="membership-info">
          <text class="membership-title">{{userInfo.vip.status ? 'VIP会员' : '免费用户'}}</text>
          <text wx:if="{{userInfo.vip.status}}" class="membership-subtitle">{{vipExpireText}}</text>
          <text wx:else class="membership-subtitle">升级VIP享受更多权益</text>
        </view>
      </view>

      <view class="membership-right">
        <view class="membership-btn" bindtap="onMembershipAction" catchtap="onMembershipAction">
          <text class="btn-text">{{userInfo.vip.status ? '续杯' : '看一看'}}</text>
        </view>
      </view>
    </view>
  </view>


  <!-- 会员功能区域 -->
  <view class="membership-section">
    <view class="section-title">
      <text class="section-icon">👑</text>
      <text class="section-text">会员功能</text>
    </view>

    <view class="settings-list">
      <view class="setting-item" bindtap="onViewMembershipBenefits">
        <view class="setting-left">
          <view class="setting-icon">💎</view>
          <view class="setting-info">
            <text class="setting-title">会员权益</text>
            <text class="setting-desc">查看{{userInfo.vip.status ? 'VIP会员' : '免费用户'}}专享功能和权益</text>
          </view>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <view class="setting-item" bindtap="onGoToStore">
        <view class="setting-left">
          <view class="setting-icon">🛒</view>
          <view class="setting-info">
            <text class="setting-title">积分商店</text>
            <text class="setting-desc">使用积分兑换VIP会员和其他奖励</text>
          </view>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <view class="setting-item" bindtap="onShowRedeemModal">
        <view class="setting-left">
          <view class="setting-icon">🎫</view>
          <view class="setting-info">
            <text class="setting-title">使用兑换码</text>
            <text class="setting-desc">输入兑换码获取VIP会员时长</text>
          </view>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <view class="setting-item" bindtap="onGoToDataManagement">
        <view class="setting-left">
          <view class="setting-icon">📊</view>
          <view class="setting-info">
            <text class="setting-title">数据管理</text>
            <text class="setting-desc">数据同步、备份、导出和历史记录</text>
          </view>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <view class="setting-item" bindtap="onGoToStatistics">
        <view class="setting-left">
          <view class="setting-icon">📈</view>
          <view class="setting-info">
            <text class="setting-title">数据统计</text>
            <text class="setting-desc">查看工作时长、收入分析和效率统计</text>
          </view>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <view wx:if="{{!userInfo.vip.status}}" class="setting-item upgrade-item" bindtap="onUpgradeMembership">
        <view class="setting-left">
          <view class="setting-icon">⭐</view>
          <view class="setting-info">
            <text class="setting-title">获取VIP会员</text>
            <text class="setting-desc">解锁高级功能，享受更多权益</text>
          </view>
        </view>
        <view class="upgrade-badge">获取</view>
      </view>
    </view>
  </view>



  <!-- 显示设置 -->
  <view class="settings-section">
    <view class="section-title">
      <text class="section-icon">🎨</text>
      <text class="section-text">显示设置</text>
    </view>
    
    <view class="settings-list">

      <view class="setting-item">
        <view class="setting-left">
          <view class="setting-icon">💱</view>
          <view class="setting-info">
            <text class="setting-title">显示货币</text>
            <text class="setting-desc">收入金额显示的货币符号</text>
          </view>
        </view>
        <view class="setting-right">
          <picker mode="selector" range="{{currencyOptions}}" range-key="label" value="{{selectedCurrencyIndex}}" bindchange="onCurrencyChange">
            <view class="picker-value">{{currencyOptions[selectedCurrencyIndex].label}}</view>
          </picker>
        </view>
      </view>

    </view>
  </view>



  <!-- 应用信息 -->
  <view class="settings-section">
    <view class="section-title">
      <text class="section-icon">ℹ️</text>
      <text class="section-text">应用信息</text>
    </view>
    
    <view class="settings-list">
      <view class="setting-item" bindtap="onViewChangelog">
        <view class="setting-left">
          <view class="setting-icon">📋</view>
          <view class="setting-info">
            <text class="setting-title">更新日志</text>
            <text class="setting-desc">查看应用版本更新记录</text>
          </view>
        </view>
        <view class="setting-arrow">›</view>
      </view>
      
      <view class="setting-item" bindtap="onAbout">
        <view class="setting-left">
          <view class="setting-icon">📱</view>
          <view class="setting-info">
            <text class="setting-title">关于应用</text>
            <text class="setting-desc">版本信息和开发者信息</text>
          </view>
        </view>
        <view class="setting-arrow">›</view>
      </view>


    </view>
  </view>

  <!-- 用户信息编辑模态框 -->
  <view class="user-edit-modal {{showUserEditModal ? 'show' : ''}}" bindtap="onCloseUserEditModal">
    <view class="modal-content" catchtap="onModalContentTap">
      <view class="modal-header">
        <text class="modal-title">编辑个人信息</text>
        <view class="modal-close" bindtap="onCloseUserEditModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 头像编辑 -->
        <view class="avatar-edit-section">
          <button class="modal-avatar" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
            <image wx:if="{{tempUserInfo.avatar}}" src="{{tempUserInfo.avatar}}" class="modal-avatar-image" />
            <view wx:else class="modal-avatar-placeholder">{{tempUserInfo.nickname.charAt(0)}}</view>
            <view class="modal-avatar-overlay">
              <text class="modal-avatar-text">点击更换头像</text>
            </view>
          </button>
        </view>

        <!-- 昵称编辑 -->
        <view class="nickname-edit-section">
          <input
            type="nickname"
            class="modal-nickname-input"
            placeholder="请输入昵称"
            value="{{tempUserInfo.nickname}}"
            bindinput="onTempNicknameInput"
            maxlength="20"
            focus="{{focusNickname}}"
          />
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="onCloseUserEditModal">取消</button>
        <button class="modal-btn confirm" bindtap="onSaveUserInfo">保存</button>
      </view>
    </view>
  </view>

  <!-- VIP记录模态框 -->
  <view class="vip-records-modal {{showVipRecordsModal ? 'show' : ''}}" bindtap="onCloseVipRecordsModal">
    <view class="modal-content" catchtap="onModalContentTap">
      <view class="modal-header">
        <text class="modal-title">VIP获取记录</text>
        <view class="modal-close" bindtap="onCloseVipRecordsModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 统计信息 -->
        <view wx:if="{{vipRecordsStats}}" class="records-stats">
          <view class="stats-item">
            <text class="stats-label">总获取天数</text>
            <text class="stats-value">{{vipRecordsStats.totalDays}}天</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">获取次数</text>
            <text class="stats-value">{{vipRecordsStats.totalRecords}}次</text>
          </view>
        </view>

        <!-- 记录列表 -->
        <view class="records-list">
          <view wx:if="{{vipRecords.length === 0}}" class="empty-records">
            <text class="empty-text">暂无VIP获取记录</text>
          </view>

          <view wx:for="{{vipRecords}}" wx:key="_id" class="record-item">
            <view class="record-icon">
              <text class="icon-text">{{item.type === 'new_user' ? '🎁' : item.type === 'activation_code' ? '🔑' : item.type === 'watch_ad' ? '📺' : item.type === 'invite_friend' ? '👥' : '💎'}}</text>
            </view>

            <view class="record-content">
              <view class="record-title">{{item.description}}</view>
              <view class="record-details">
                <text class="record-date">{{item.createTimeText}}</text>
                <text class="record-days">+{{item.days}}天</text>
              </view>
              <view wx:if="{{item.source && item.source !== 'system' && item.source !== 'ad_reward'}}" class="record-source">
                <text class="source-text">来源：{{item.source}}</text>
              </view>
            </view>

            <view class="record-status">
              <text class="status-text {{item.status === 'active' ? 'active' : 'expired'}}">
                {{item.status === 'active' ? '有效' : '已过期'}}
              </text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view wx:if="{{hasMoreVipRecords}}" class="load-more" bindtap="onLoadMoreVipRecords">
          <text class="load-more-text">加载更多</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 兑换码使用模态框 -->
  <redeem-code-modal
    show="{{showRedeemModal}}"
    default-code="{{defaultRedeemCode}}"
    bind:success="onRedeemSuccess"
    bind:close="onCloseRedeemModal"
  />
</view>