// 签到页面
Page({
  data: {
    // 签到状态
    checkInStatus: {
      hasCheckedInToday: false,
      consecutiveDays: 0,
      totalDays: 0,
      longestStreak: 0,
      nextReward: 1
    },
    
    // 签到日历
    calendar: {
      currentMonth: '',
      checkIns: [],
      days: []
    },
    
    // 签到历史
    history: [],
    
    // 加载状态
    loading: {
      checkIn: false,
      status: true, // 初始状态为加载中
      calendar: false,
      history: false
    },
    
    // 当前显示的月份
    currentYearMonth: ''
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('签到页面加载')
    
    // 设置当前月份
    const now = new Date()
    const yearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
    this.setData({
      currentYearMonth: yearMonth
    })
    
    // 加载数据
    this.loadCheckInStatus()
    this.loadCheckInCalendar(yearMonth)
    this.loadCheckInHistory()
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('签到页面显示')
    // 刷新签到状态
    this.loadCheckInStatus()
  },

  /**
   * 加载签到状态
   */
  async loadCheckInStatus() {
    try {
      this.setData({
        'loading.status': true
      })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getCheckInStatus',
          data: {}
        }
      })

      if (result.result.success) {
        this.setData({
          checkInStatus: result.result.data
        })
      } else {
        wx.showToast({
          title: result.result.errMsg || '获取签到状态失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载签到状态失败:', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.status': false
      })
    }
  },

  /**
   * 加载签到日历
   */
  async loadCheckInCalendar(yearMonth) {
    try {
      this.setData({
        'loading.calendar': true
      })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getCheckInCalendar',
          data: {
            yearMonth: yearMonth
          }
        }
      })

      if (result.result.success) {
        const checkIns = result.result.data.checkIns
        const calendarDays = this.generateCalendarData(yearMonth, checkIns)

        this.setData({
          'calendar.currentMonth': yearMonth,
          'calendar.checkIns': checkIns,
          'calendar.days': calendarDays
        })
      } else {
        console.error('获取签到日历失败:', result.result.errMsg)
      }
    } catch (error) {
      console.error('加载签到日历失败:', error)
    } finally {
      this.setData({
        'loading.calendar': false
      })
    }
  },

  /**
   * 加载签到历史
   */
  async loadCheckInHistory() {
    try {
      this.setData({
        'loading.history': true
      })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getCheckInHistory',
          data: {
            limit: 10
          }
        }
      })

      if (result.result.success) {
        this.setData({
          history: result.result.data.history
        })
      } else {
        console.error('获取签到历史失败:', result.result.errMsg)
      }
    } catch (error) {
      console.error('加载签到历史失败:', error)
    } finally {
      this.setData({
        'loading.history': false
      })
    }
  },

  /**
   * 执行签到
   */
  async onCheckIn() {
    if (this.data.checkInStatus.hasCheckedInToday) {
      wx.showToast({
        title: '今日已签到',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({
        'loading.checkIn': true
      })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'checkIn',
          data: {}
        }
      })

      if (result.result.success) {
        const data = result.result.data
        
        // 显示签到成功动画
        this.showCheckInSuccess(data)
        
        // 刷新数据
        this.loadCheckInStatus()
        this.loadCheckInCalendar(this.data.currentYearMonth)
        this.loadCheckInHistory()
        
      } else {
        wx.showToast({
          title: result.result.errMsg || '签到失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('签到失败:', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.checkIn': false
      })
    }
  },

  /**
   * 显示签到成功动画
   */
  showCheckInSuccess(data) {
    const { reward, consecutiveDays } = data
    
    wx.showModal({
      title: '签到成功！',
      content: `连续签到 ${consecutiveDays} 天\n获得奖励：${reward} 积分`,
      showCancel: false,
      confirmText: '太棒了',
      success: () => {
        // 可以在这里添加更多的成功反馈
      }
    })
  },

  /**
   * 切换月份
   */
  onMonthChange(event) {
    const { type } = event.currentTarget.dataset
    const currentDate = new Date(this.data.currentYearMonth + '-01')
    
    if (type === 'prev') {
      currentDate.setMonth(currentDate.getMonth() - 1)
    } else if (type === 'next') {
      currentDate.setMonth(currentDate.getMonth() + 1)
    }
    
    const newYearMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`
    
    this.setData({
      currentYearMonth: newYearMonth
    })
    
    this.loadCheckInCalendar(newYearMonth)
  },

  /**
   * 查看奖励规则
   */
  onViewRewardRules() {
    const content = `签到奖励规则：

基础奖励：
• 每日签到：1 积分
• 连续7天：5 积分

里程碑奖励：
• 连续30天：额外10积分
• 连续100天：额外30积分
• 连续365天：额外100积分

VIP特权：
• VIP用户奖励1.5倍

积分可用于兑换VIP会员时长等奖励！`

    wx.showModal({
      title: '奖励规则',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 检查日期是否已签到
   */
  isDateCheckedIn(date) {
    return this.data.calendar.checkIns.some(checkIn => checkIn.date === date)
  },

  /**
   * 格式化日期显示
   */
  formatDate(dateStr) {
    const date = new Date(dateStr)
    return `${date.getMonth() + 1}月${date.getDate()}日`
  },

  /**
   * 格式化时间显示
   */
  formatTime(checkInAt) {
    const date = new Date(checkInAt)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days === 0) {
      return '今天'
    } else if (days === 1) {
      return '昨天'
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  },

  /**
   * 生成日历数据
   */
  generateCalendarData(yearMonth, checkIns) {
    const [year, month] = yearMonth.split('-').map(Number)
    const firstDay = new Date(year, month - 1, 1)
    const lastDay = new Date(year, month, 0)
    const daysInMonth = lastDay.getDate()
    const startWeekday = firstDay.getDay()

    const calendarDays = []

    // 添加上个月的日期（填充）
    for (let i = 0; i < startWeekday; i++) {
      calendarDays.push({
        day: '',
        date: '',
        isCurrentMonth: false,
        isCheckedIn: false
      })
    }

    // 添加当月的日期
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
      const isCheckedIn = checkIns.some(checkIn => checkIn.date === dateStr)

      calendarDays.push({
        day: day,
        date: dateStr,
        isCurrentMonth: true,
        isCheckedIn: isCheckedIn
      })
    }

    return calendarDays
  }
})
