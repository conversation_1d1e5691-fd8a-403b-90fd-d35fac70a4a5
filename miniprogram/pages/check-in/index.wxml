<view class="check-in-page">
  <!-- 签到状态卡片 -->
  <view class="status-card">
    <view class="status-header">
      <view class="status-icon">
        <text class="icon">{{checkInStatus.hasCheckedInToday ? '✅' : '📅'}}</text>
      </view>
      <view class="status-info">
        <view class="status-title">
          {{checkInStatus.hasCheckedInToday ? '今日已签到' : '今日未签到'}}
        </view>
        <view class="status-subtitle">
          连续签到 {{checkInStatus.consecutiveDays}} 天
        </view>
      </view>

    </view>
    
    <view class="status-stats">
      <view class="stat-item">
        <view class="stat-number">{{checkInStatus.totalDays}}</view>
        <view class="stat-label">累计签到</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{checkInStatus.consecutiveDays}}</view>
        <view class="stat-label">连续签到</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{checkInStatus.longestStreak}}</view>
        <view class="stat-label">最长连续</view>
      </view>
    </view>
    
    <view class="check-in-action">
      <button
        class="check-in-btn {{checkInStatus.hasCheckedInToday ? 'checked' : ''}}"
        bindtap="onCheckIn"
        disabled="{{checkInStatus.hasCheckedInToday || loading.checkIn || loading.status}}"
        loading="{{loading.checkIn}}"
      >
        <text wx:if="{{loading.status}}">加载中...</text>
        <text wx:elif="{{checkInStatus.hasCheckedInToday}}">已签到</text>
        <text wx:else>签到 +{{checkInStatus.nextReward}}</text>
      </button>
    </view>
  </view>

  <!-- 签到日历 -->
  <view class="calendar-section">
    <view class="section-header">
      <view class="section-title">签到日历</view>
      <view class="month-nav">
        <view class="nav-btn" bindtap="onMonthChange" data-type="prev">‹</view>
        <view class="current-month">{{currentYearMonth}}</view>
        <view class="nav-btn" bindtap="onMonthChange" data-type="next">›</view>
      </view>
    </view>
    
    <view class="calendar-grid">
      <view class="calendar-header">
        <view class="day-header">日</view>
        <view class="day-header">一</view>
        <view class="day-header">二</view>
        <view class="day-header">三</view>
        <view class="day-header">四</view>
        <view class="day-header">五</view>
        <view class="day-header">六</view>
      </view>
      
      <view wx:if="{{loading.calendar}}" class="calendar-loading">
        <text>加载中...</text>
      </view>
      <view wx:else class="calendar-body">
        <view wx:for="{{calendar.days}}" wx:key="index"
              class="calendar-day {{item.isCurrentMonth ? 'current-month' : 'other-month'}} {{item.isCheckedIn ? 'checked' : ''}}">
          <view wx:if="{{item.isCurrentMonth}}" class="day-number">{{item.day}}</view>
          <view wx:if="{{item.isCheckedIn}}" class="check-mark">✓</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 奖励规则 -->
  <view class="reward-section">
    <view class="section-header">
      <view class="section-title">奖励规则</view>
      <view class="view-more" bindtap="onViewRewardRules">查看详情 ›</view>
    </view>
    
    <view class="reward-rules">
      <view class="rule-item">
        <view class="rule-icon">📅</view>
        <view class="rule-text">每日签到 +1积分</view>
      </view>
      <view class="rule-item">
        <view class="rule-icon">🔥</view>
        <view class="rule-text">连续7天 +5积分</view>
      </view>
      <view class="rule-item">
        <view class="rule-icon">💎</view>
        <view class="rule-text">VIP用户奖励1.5倍</view>
      </view>
    </view>
  </view>

  <!-- 签到历史 -->
  <view class="history-section">
    <view class="section-header">
      <view class="section-title">最近签到</view>
    </view>
    
    <view wx:if="{{history.length > 0}}" class="history-list">
      <view wx:for="{{history}}" wx:key="_id" class="history-item">
        <view class="history-date">
          <view class="date-text">{{formatDate(item.date)}}</view>
          <view class="time-text">{{formatTime(item.checkInAt)}}</view>
        </view>
        <view class="history-info">
          <view class="consecutive-days">连续{{item.consecutiveDays}}天</view>
          <view class="reward">+{{item.reward}}积分</view>
        </view>
      </view>
    </view>
    
    <view wx:else class="empty-history">
      <view class="empty-icon">📝</view>
      <view class="empty-text">暂无签到记录</view>
    </view>
  </view>
</view>
