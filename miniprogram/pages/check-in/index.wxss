/* 签到页面样式 */
.check-in-page {
  padding: 32rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 签到状态卡片 */
.status-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.status-icon {
  margin-right: 20rpx;
}

.status-icon .icon {
  font-size: 48rpx;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.status-subtitle {
  font-size: 26rpx;
  color: #666;
}



.status-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32rpx;
  padding: 24rpx 0;
  border-top: 2rpx solid #f5f5f5;
  border-bottom: 2rpx solid #f5f5f5;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.check-in-action {
  text-align: center;
}

.check-in-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 700;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.check-in-btn:active {
  transform: scale(0.98);
}

.check-in-btn.checked {
  background: #e9ecef;
  color: #6c757d;
  box-shadow: none;
}

.check-in-btn[disabled] {
  opacity: 0.6;
}

/* 通用区块样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.view-more {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
}

/* 日历区块 */
.calendar-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.month-nav {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nav-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
  cursor: pointer;
}

.nav-btn:active {
  background: #e9ecef;
}

.current-month {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.calendar-grid {
  margin-top: 24rpx;
  width: 100%;
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.day-header {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 12rpx 0;
}

.calendar-body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  width: 100%;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  position: relative;
  min-height: 80rpx;
  width: 100%;
  box-sizing: border-box;
}

.calendar-day.current-month {
  color: #333;
}

.calendar-day.other-month {
  color: transparent;
}

.calendar-day.checked {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.day-number {
  font-size: 24rpx;
  font-weight: 600;
}

.check-mark {
  font-size: 16rpx;
  margin-top: 4rpx;
}

.calendar-loading {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
  width: 100%;
}

/* 奖励规则区块 */
.reward-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.reward-rules {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.rule-icon {
  font-size: 32rpx;
}

.rule-text {
  font-size: 28rpx;
  color: #333;
}

/* 签到历史区块 */
.history-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.history-item:last-child {
  border-bottom: none;
}

.history-date {
  flex: 1;
}

.date-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.time-text {
  font-size: 24rpx;
  color: #999;
}

.history-info {
  text-align: center;
  margin: 0 24rpx;
}

.consecutive-days {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.reward {
  font-size: 28rpx;
  color: #28a745;
  font-weight: 700;
}

.history-type {
  min-width: 80rpx;
  text-align: right;
}



/* 空状态 */
.empty-history {
  text-align: center;
  padding: 60rpx 0;
}

.empty-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
