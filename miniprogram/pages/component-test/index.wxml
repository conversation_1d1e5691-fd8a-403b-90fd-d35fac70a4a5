<!-- 组件测试页面 -->
<view class="test-container">
  <view class="test-header">
    <text class="test-title">摸鱼特效组件测试</text>
  </view>
  
  <view class="test-controls">
    <button 
      class="test-btn {{isFishing ? 'stop' : 'start'}}" 
      bindtap="toggleFishing">
      {{isFishing ? '停止摸鱼' : '开始摸鱼'}}
    </button>
    
    <view class="test-status">
      <text>当前状态: {{isFishing ? '摸鱼中 🐟' : '工作中 💼'}}</text>
    </view>
  </view>
  
  <!-- 摸鱼特效组件 -->
  <fishing-effect is-fishing="{{isFishing}}"></fishing-effect>
</view>
