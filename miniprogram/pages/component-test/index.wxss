/* 组件测试页面样式 */
.test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.test-header {
  margin-bottom: 60rpx;
  text-align: center;
}

.test-title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.test-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40rpx;
  z-index: 1000;
  position: relative;
}

.test-btn {
  padding: 24rpx 48rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.test-btn.start {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
}

.test-btn.stop {
  background: linear-gradient(45deg, #f44336, #da190b);
  color: white;
}

.test-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.test-status {
  padding: 20rpx 40rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.test-status text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
