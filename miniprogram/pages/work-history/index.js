// 工作履历页面逻辑 - 重构版
const { formatDate, formatDateToYYYYMMDD } = require('../../utils/helpers/time-utils.js')
const { WorkHistoryService } = require('../../core/services/work-history-service.js')

Page({
  data: {
    // 工作履历列表
    workHistoryList: [],
    currentWorkId: null,
    
    // 折叠面板状态
    expandedItems: {},
    
    // 表单数据
    showAddModal: false,
    editingWorkId: null,
    formData: {
      company: '',
      position: '',
      startDate: '',
      probationSalary: '',
      probationEndDate: '',
      formalSalary: '',
      endDate: '',
      payDays: [],
      notes: ''
    },
    
    // 日期选择器
    datePickerType: '',
    datePickerValue: '',
    
    // 验证错误
    errors: {}
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('工作履历页面加载开始')
    
    // 初始化服务
    this.workHistoryService = new WorkHistoryService()
    
    // 获取全局数据管理器
    this.dataManager = getApp().getDataManager()
    
    // 加载工作履历列表
    this.loadWorkHistoryList()
    
    // 注册数据变化监听器
    this.registerDataChangeListener()
    
    console.log('工作履历页面加载完成')
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('工作履历页面显示')
    
    // 刷新数据
    this.loadWorkHistoryList()
  },

  /**
   * 页面卸载时
   */
  onUnload() {
    console.log('工作履历页面卸载')
    
    // 移除数据变化监听器
    this.unregisterDataChangeListener()
  },

  /**
   * 注册数据变化监听器
   */
  registerDataChangeListener() {
    this.dataChangeListener = (userData) => {
      console.log('收到数据变化通知，刷新工作履历列表')
      this.loadWorkHistoryList()
    }
    
    this.dataManager.addChangeListener(this.dataChangeListener)
  },

  /**
   * 移除数据变化监听器
   */
  unregisterDataChangeListener() {
    if (this.dataChangeListener) {
      this.dataManager.removeChangeListener(this.dataChangeListener)
      this.dataChangeListener = null
    }
  },

  /**
   * 加载工作履历列表
   */
  loadWorkHistoryList() {
    try {
      const workHistoryList = this.workHistoryService.getAllWorkHistory()
      const currentWorkId = this.workHistoryService.getCurrentWorkId()
      
      // 为列表数据添加显示信息
      const processedList = workHistoryList.map(work => {
        return Object.assign({}, work, {
          displayName: this.workHistoryService.getWorkDisplayName(work),
          status: this.workHistoryService.getWorkStatus(work),
          timeRangeText: this.workHistoryService.getWorkTimeRangeText(work),
          isCurrent: work.id === currentWorkId
        })
      })
      
      this.setData({
        workHistoryList: processedList,
        currentWorkId
      })
      
      console.log(`工作履历列表加载完成，共${processedList.length}条记录`)
    } catch (error) {
      console.error('加载工作履历失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 切换折叠面板
   */
  toggleExpanded(e) {
    const workId = e.currentTarget.dataset.workId
    const expandedItems = { ...this.data.expandedItems }
    expandedItems[workId] = !expandedItems[workId]
    
    this.setData({ expandedItems })
  },

  /**
   * 显示添加工作履历模态框
   */
  showAddModal() {
    this.setData({
      showAddModal: true,
      editingWorkId: null,
      formData: {
        company: '',
        position: '',
        startDate: '',
        probationSalary: '',
        probationEndDate: '',
        formalSalary: '',
        endDate: '',
        notes: '',
        payDays: []
      },
      errors: {}
    })
  },

  /**
   * 显示编辑工作履历模态框
   */
  showEditWorkModal(e) {
    const workId = e.currentTarget.dataset.workId
    const work = this.workHistoryService.getWorkHistory(workId)
    
    if (!work) {
      wx.showToast({
        title: '工作履历不存在',
        icon: 'none'
      })
      return
    }
    
    this.setData({
      showAddModal: true,
      editingWorkId: workId,
      formData: {
        company: work.company || '',
        position: work.position || '',
        startDate: work.startDate ? formatDateToYYYYMMDD(work.startDate) : '',
        probationSalary: work.probationSalary ? work.probationSalary.toString() : '',
        probationEndDate: work.probationEndDate ? formatDateToYYYYMMDD(work.probationEndDate) : '',
        formalSalary: work.formalSalary ? work.formalSalary.toString() : '',
        endDate: work.endDate ? formatDateToYYYYMMDD(work.endDate) : '',
        notes: work.notes || '',
        payDays: work.payDays || []
      },
      errors: {}
    })
  },

  /**
   * 隐藏模态框
   */
  hideModal() {
    this.setData({
      showAddModal: false,
      editingWorkId: null,
      formData: {
        company: '',
        position: '',
        startDate: '',
        probationSalary: '',
        probationEndDate: '',
        formalSalary: '',
        endDate: '',
        notes: '',
        payDays: []
      },
      errors: {}
    })
  },

  /**
   * 阻止模态框关闭
   */
  preventModalClose() {
    // 阻止事件冒泡，防止点击模态框内容区域时关闭模态框
  },

  /**
   * 输入框内容变化
   */
  onInputChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    
    const formData = { ...this.data.formData }
    formData[field] = value
    
    // 清除该字段的错误信息
    const errors = { ...this.data.errors }
    if (errors[field]) {
      delete errors[field]
    }
    
    this.setData({
      formData,
      errors
    })
  },

  /**
   * 工资输入框内容变化（支持小数，最多两位）
   */
  onSalaryInputChange(e) {
    const field = e.currentTarget.dataset.field
    let value = e.detail.value

    // 限制小数位数最多2位
    value = this.limitDecimalPlaces(value, 2)

    const formData = { ...this.data.formData }
    formData[field] = value

    // 清除该字段的错误信息
    const errors = { ...this.data.errors }
    if (errors[field]) {
      delete errors[field]
    }

    this.setData({
      formData,
      errors
    })
  },

  /**
   * 限制小数位数
   */
  limitDecimalPlaces(value, maxDecimalPlaces) {
    if (!value || value === '') return ''

    // 移除非数字和小数点的字符
    value = value.replace(/[^\d.]/g, '')

    // 确保只有一个小数点
    const parts = value.split('.')
    if (parts.length > 2) {
      // 保留第一个小数点，移除后续的小数点
      value = parts[0] + '.' + parts.slice(1).join('').replace(/\./g, '')
    }

    // 重新分割以获取正确的部分
    const finalParts = value.split('.')

    // 限制小数位数
    if (finalParts.length === 2 && finalParts[1].length > maxDecimalPlaces) {
      value = finalParts[0] + '.' + finalParts[1].substring(0, maxDecimalPlaces)
    }

    return value
  },

  /**
   * 日期选择器变化
   */
  onDateChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    
    const formData = { ...this.data.formData }
    formData[field] = value
    
    // 清除该字段的错误信息
    const errors = { ...this.data.errors }
    if (errors[field]) {
      delete errors[field]
    }
    
    this.setData({
      formData,
      errors
    })
  },

  /**
   * 清除日期
   */
  clearDate(e) {
    const field = e.currentTarget.dataset.field

    const formData = { ...this.data.formData }
    formData[field] = ''

    // 清除该字段的错误信息
    const errors = { ...this.data.errors }
    if (errors[field]) {
      delete errors[field]
    }

    this.setData({
      formData,
      errors
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data
    const errors = {}
    
    // 必填字段验证
    if (!formData.company.trim()) {
      errors.company = '请输入工作单位'
    }
    
    if (!formData.position.trim()) {
      errors.position = '请输入工作职位'
    }
    
    if (!formData.startDate) {
      errors.startDate = '请选择入职时间'
    }
    
    // 日期逻辑验证
    if (formData.startDate && formData.probationEndDate) {
      const startDate = new Date(formData.startDate)
      const probationEndDate = new Date(formData.probationEndDate)
      
      if (probationEndDate <= startDate) {
        errors.probationEndDate = '转正日期必须在入职时间之后'
      }
    }
    
    if (formData.startDate && formData.endDate) {
      const startDate = new Date(formData.startDate)
      const endDate = new Date(formData.endDate)
      
      if (endDate <= startDate) {
        errors.endDate = '离职时间必须在入职时间之后'
      }
    }
    
    // 工资验证
    if (formData.probationSalary) {
      const probationSalaryNum = Number(formData.probationSalary)
      if (isNaN(probationSalaryNum)) {
        errors.probationSalary = '请输入有效的试用工资'
      } else if (probationSalaryNum < 0) {
        errors.probationSalary = '试用工资不能为负数'
      }
    }

    if (formData.formalSalary) {
      const formalSalaryNum = Number(formData.formalSalary)
      if (isNaN(formalSalaryNum)) {
        errors.formalSalary = '请输入有效的正式工资'
      } else if (formalSalaryNum < 0) {
        errors.formalSalary = '正式工资不能为负数'
      }
    }

    // 发薪日验证
    if (formData.payDays && formData.payDays.length > 0) {
      const validation = this.workHistoryService.validatePayDays(formData.payDays)
      if (!validation.isValid) {
        errors.payDays = validation.errors.join(', ')
      }
    }

    this.setData({ errors })

    return Object.keys(errors).length === 0
  },

  /**
   * 保存工作履历
   */
  saveWorkHistory() {
    if (!this.validateForm()) {
      return
    }
    
    const { formData, editingWorkId } = this.data
    
    try {
      // 构建工作履历数据
      const workData = {
        company: formData.company.trim(),
        position: formData.position.trim(),
        startDate: new Date(formData.startDate),
        probationSalary: formData.probationSalary ? Number(formData.probationSalary) : 0,
        probationEndDate: formData.probationEndDate ? new Date(formData.probationEndDate) : null,
        formalSalary: formData.formalSalary ? Number(formData.formalSalary) : 0,
        endDate: formData.endDate ? new Date(formData.endDate) : null,
        notes: formData.notes.trim(),
        payDays: formData.payDays || []
      }
      
      if (editingWorkId) {
        // 更新现有工作履历
        this.workHistoryService.updateWorkHistory(editingWorkId, workData)
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        })
      } else {
        // 添加新工作履历
        const workId = this.workHistoryService.addWorkHistory(workData)
        
        // 如果是第一个工作履历，自动设置为当前工作
        if (this.data.workHistoryList.length === 0) {
          this.workHistoryService.setCurrentWork(workId)
        }
        
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        })
      }
      
      this.hideModal()
      // 数据变化监听器会自动刷新列表
    } catch (error) {
      console.error('保存工作履历失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 设置为当前工作
   */
  setCurrentWork(e) {
    const workId = e.currentTarget.dataset.workId
    
    try {
      this.workHistoryService.setCurrentWork(workId)
      wx.showToast({
        title: '设置成功',
        icon: 'success'
      })
      // 数据变化监听器会自动刷新列表
    } catch (error) {
      console.error('设置当前工作失败:', error)
      wx.showToast({
        title: '设置失败',
        icon: 'none'
      })
    }
  },

  /**
   * 删除工作履历
   */
  deleteWorkHistory(e) {
    const workId = e.currentTarget.dataset.workId
    const work = this.workHistoryService.getWorkHistory(workId)
    
    if (!work) {
      wx.showToast({
        title: '工作履历不存在',
        icon: 'none'
      })
      return
    }
    
    const workDisplayName = this.workHistoryService.getWorkDisplayName(work)
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${workDisplayName}"的工作履历吗？\n\n删除后该工作的所有时间追踪数据也将被清除，此操作无法撤销。`,
      confirmText: '删除',
      confirmColor: '#FF3B30',
      success: (res) => {
        if (res.confirm) {
          try {
            this.workHistoryService.deleteWorkHistory(workId)
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
            // 数据变化监听器会自动刷新列表
          } catch (error) {
            console.error('删除工作履历失败:', error)
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  /**
   * 添加发薪日
   */
  addPayDay() {
    const formData = { ...this.data.formData }

    // 找一个未使用的日期
    let newDay = 15
    const existingDays = formData.payDays.map(p => p.day)

    for (let day = 1; day <= 31; day++) {
      if (!existingDays.includes(day)) {
        newDay = day
        break
      }
    }

    formData.payDays.push({
      day: newDay,
      name: '新发薪日'
    })

    this.setData({ formData })
  },

  /**
   * 删除发薪日
   */
  removePayDay(e) {
    const index = e.currentTarget.dataset.index
    const formData = { ...this.data.formData }
    formData.payDays.splice(index, 1)

    this.setData({ formData })
  },

  /**
   * 更新发薪日字段
   */
  onPayDayChange(e) {
    const { index, field } = e.currentTarget.dataset
    const value = e.detail.value
    const formData = { ...this.data.formData }

    if (field === 'day') {
      // picker返回的是索引，需要转换为实际日期（1-31）
      formData.payDays[index].day = parseInt(value) + 1
    } else if (field === 'name') {
      formData.payDays[index].name = value
    }

    this.setData({ formData })
  },


})