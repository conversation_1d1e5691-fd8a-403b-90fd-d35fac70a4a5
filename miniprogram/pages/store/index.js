// 积分商店页面
Page({
  data: {
    // 当前积分
    currentPoints: 0,

    // 商店商品
    storeItems: [],

    // 是否为测试用户
    isTestUser: false,

    // 加载状态
    loading: {
      items: false,
      purchase: false
    },

    // 我的兑换码
    myCodes: [],

    // 当前选中的标签页
    activeTab: 'store', // 'store' | 'codes'

    // 兑换码模态框
    showRedeemModal: false,
    defaultRedeemCode: ''
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('积分商店页面加载')
    this.loadStoreItems()
    this.loadMyCodes()
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('积分商店页面显示')
    // 刷新数据
    this.loadStoreItems()
    this.loadMyCodes()
  },

  /**
   * 加载商店商品
   */
  async loadStoreItems() {
    try {
      this.setData({
        'loading.items': true
      })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getStoreItems',
          data: {}
        }
      })

      if (result.result.success) {
        this.setData({
          storeItems: result.result.data.items,
          currentPoints: result.result.data.currentPoints,
          isTestUser: result.result.data.isTestUser || false
        })
      } else {
        wx.showToast({
          title: result.result.errMsg || '加载商品失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载商店商品失败:', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.items': false
      })
    }
  },

  /**
   * 加载我的兑换码
   */
  async loadMyCodes() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getMyRedemptionCodes',
          data: {
            limit: 20
          }
        }
      })

      if (result.result.success) {
        this.setData({
          myCodes: result.result.data.codes
        })
      } else {
        console.error('加载兑换码失败:', result.result.errMsg)
      }
    } catch (error) {
      console.error('加载兑换码失败:', error)
    }
  },



  /**
   * 购买商品
   */
  async onPurchaseItem(event) {
    const dataset = event.currentTarget.dataset
    const itemId = dataset.itemId

    if (!itemId) {
      wx.showToast({
        title: '商品ID获取失败',
        icon: 'none'
      })
      return
    }

    const item = this.data.storeItems.find(item => item.id === itemId)

    if (!item) {
      wx.showToast({
        title: '商品不存在',
        icon: 'none'
      })
      return
    }

    // 检查积分余额
    if (this.data.currentPoints < item.pointsCost) {
      wx.showToast({
        title: '积分余额不足',
        icon: 'none'
      })
      return
    }

    // 确认购买
    const confirmResult = await this.showPurchaseConfirm(item)
    if (!confirmResult) {
      return
    }

    try {
      this.setData({
        'loading.purchase': true
      })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'purchaseItem',
          data: {
            itemId: itemId
          }
        }
      })

      if (result.result.success) {
        const data = result.result.data
        
        // 显示购买成功
        this.showPurchaseSuccess(data)
        
        // 刷新数据
        this.loadStoreItems()
        this.loadMyCodes()
        
      } else {
        wx.showToast({
          title: result.result.errMsg || '购买失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('购买失败:', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.purchase': false
      })
    }
  },

  /**
   * 显示购买确认对话框
   */
  showPurchaseConfirm(item) {
    return new Promise((resolve) => {
      wx.showModal({
        title: '确认购买',
        content: `确定要花费 ${item.pointsCost} 积分购买 ${item.name} 吗？`,
        confirmText: '确认购买',
        cancelText: '取消',
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  },

  /**
   * 显示购买成功
   */
  showPurchaseSuccess(data) {
    const { item, redemptionCode } = data
    
    wx.showModal({
      title: '购买成功！',
      content: `已获得 ${item.name}\n兑换码：${redemptionCode.code}\n\n兑换码已保存到"我的兑换码"中`,
      showCancel: false,
      confirmText: '查看兑换码',
      success: () => {
        this.setData({
          activeTab: 'codes'
        })
      }
    })
  },

  /**
   * 切换标签页
   */
  onTabChange(event) {
    const { tab } = event.currentTarget.dataset
    this.setData({
      activeTab: tab
    })
  },

  /**
   * 复制兑换码
   */
  onCopyCode(event) {
    const { code } = event.currentTarget.dataset
    
    wx.setClipboardData({
      data: code,
      success: () => {
        wx.showToast({
          title: '兑换码已复制',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 使用兑换码
   */
  onUseCode(event) {
    const { code } = event.currentTarget.dataset

    this.setData({
      showRedeemModal: true,
      defaultRedeemCode: code || ''
    })
  },

  /**
   * 查看积分记录
   */
  onViewPointsRecords() {
    wx.navigateTo({
      url: '/pages/points/index'
    })
  },



  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'active': '未使用',
      'used': '已使用',
      'expired': '已过期'
    }
    return statusMap[status] || '未知'
  },

  /**
   * 获取状态样式类
   */
  getStatusClass(status) {
    const classMap = {
      'active': 'status-active',
      'used': 'status-used',
      'expired': 'status-expired'
    }
    return classMap[status] || 'status-unknown'
  },

  /**
   * 关闭兑换码模态框
   */
  onCloseRedeemModal() {
    this.setData({
      showRedeemModal: false,
      defaultRedeemCode: ''
    })
  },

  /**
   * 兑换成功回调
   */
  onRedeemSuccess(event) {
    const { code, data } = event.detail
    console.log('兑换成功:', code, data)

    // 刷新商店数据
    this.loadStoreItems()
    this.loadMyCodes()

    // 关闭模态框
    this.onCloseRedeemModal()
  }
})
