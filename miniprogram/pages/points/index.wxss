/* 积分记录页面样式 */
.points-page {
  padding: 32rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 统计卡片 */
.stats-card {
  background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(41, 128, 185, 0.3);
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 700;
}

.stats-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stats-action:active {
  background: rgba(255, 255, 255, 0.3);
}

.action-text {
  font-size: 24rpx;
  font-weight: 600;
}

.action-arrow {
  font-size: 18rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.stats-action-bar {
  text-align: center;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 24rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.btn-icon {
  font-size: 24rpx;
}

/* 积分记录卡片 */
.records-card {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.records-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx 32rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.records-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.filter-options {
  display: flex;
  gap: 8rpx;
}

.filter-option {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid transparent;
}

.filter-option.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: rgba(102, 126, 234, 0.3);
}

.filter-option:active {
  transform: scale(0.95);
}

.records-content {
  padding: 32rpx;
}

.loading-state, .empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.loading-icon, .empty-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.loading-text, .empty-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.record-item {
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s ease;
}

.record-item:last-child {
  border-bottom: none;
}

/* 积分记录类型样式 - 影响整个记录项的文字颜色 */
.record-item.type-earn .source-name,
.record-item.type-earn .record-description {
  color: #1e7e34 !important;
  font-weight: 600;
}

.record-item.type-spend .source-name,
.record-item.type-spend .record-description {
  color: #c82333 !important;
  font-weight: 600;
}

/* 时间文字保持原色 */
.record-item .record-time {
  color: #999 !important;
}

/* 为收入和支出记录添加微妙的背景色 */
.record-item.type-earn {
  background: rgba(40, 167, 69, 0.02);
  border-left: 4rpx solid rgba(40, 167, 69, 0.3);
  padding-left: 20rpx;
}

.record-item.type-spend {
  background: rgba(220, 53, 69, 0.02);
  border-left: 4rpx solid rgba(220, 53, 69, 0.3);
  padding-left: 20rpx;
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.record-source {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.source-icon {
  font-size: 32rpx;
}

.source-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.record-amount {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-weight: 700;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  padding: 12rpx 16rpx;
  border-radius: 16rpx;
  background: rgba(0, 0, 0, 0.05);
  min-width: 80rpx;
  justify-content: center;
}

.type-earn {
  color: #28a745;
  background: rgba(40, 167, 69, 0.15);
  border: 2rpx solid rgba(40, 167, 69, 0.2);
}

.type-spend {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.15);
  border: 2rpx solid rgba(220, 53, 69, 0.2);
}

.amount-sign {
  font-size: 32rpx;
  font-weight: 800;
  line-height: 1;
}

.amount-value {
  font-size: 36rpx;
  font-weight: 800;
  line-height: 1;
}

.record-body {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.record-description {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.loading-more .loading-icon {
  font-size: 32rpx;
  margin-bottom: 0;
}

.loading-more .loading-text {
  font-size: 26rpx;
  margin-bottom: 0;
}

.load-more-tip {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 获取积分任务卡片 */
.tasks-card {
  background: white;
  border-radius: 24rpx;
  margin-top: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.tasks-header {
  padding: 32rpx 32rpx 24rpx 32rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.tasks-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.tasks-subtitle {
  font-size: 24rpx;
  color: #666;
}

.tasks-content {
  padding: 24rpx 32rpx 32rpx 32rpx;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
  transition: all 0.3s ease;
}

.task-item:last-child {
  border-bottom: none;
}

.task-item.available {
  opacity: 1;
}

.task-item.completed {
  opacity: 0.7;
}

.task-item.disabled {
  opacity: 0.5;
}

.task-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.task-info {
  flex: 1;
  margin-right: 16rpx;
}

.task-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.task-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 4rpx;
}

.task-progress {
  font-size: 22rpx;
  color: #999;
}

.task-reward {
  text-align: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.reward-amount {
  font-size: 32rpx;
  font-weight: 700;
  color: #28a745;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  margin-bottom: 4rpx;
}

.reward-label {
  font-size: 20rpx;
  color: #666;
}

.task-action {
  flex-shrink: 0;
}

.action-btn {
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.primary:active {
  transform: scale(0.95);
}

.action-btn.completed {
  background: #28a745;
  color: white;
}

.action-btn.disabled {
  background: #f8f9fa;
  color: #999;
}

/* 积分来源统计卡片 */
.source-stats-card {
  background: white;
  border-radius: 24rpx;
  margin-top: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.stats-card-header {
  padding: 32rpx 32rpx 24rpx 32rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.stats-card-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.source-stats-content {
  padding: 32rpx;
}

/* 饼图样式 */
.pie-chart-container {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}

.pie-chart {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 备用背景色，如果不支持 conic-gradient */
  background: #f5f5f5;
}

.pie-chart-center {
  width: 120rpx;
  height: 120rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.total-points {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.total-label {
  font-size: 20rpx;
  color: #666;
  margin-top: 4rpx;
}

/* 图例样式 */
.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
  flex-shrink: 0;
}

.legend-info {
  flex: 1;
}

.legend-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.legend-value {
  font-size: 24rpx;
  color: #666;
}
