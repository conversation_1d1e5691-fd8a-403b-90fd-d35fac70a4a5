// 积分记录页面
Page({
  data: {
    // 积分统计
    stats: {
      totalEarned: 0,
      totalSpent: 0,
      currentBalance: 0,
      totalRecords: 0
    },
    
    // 积分记录
    records: [],
    
    // 筛选条件
    filter: {
      type: '', // '' | 'earn' | 'spend'
    },
    
    // 加载状态
    loading: {
      records: false,
      more: false,
      sourceStats: false
    },

    // 来源统计数据
    sourceStatsData: [],
    totalSourcePoints: 0,
    pieChartGradient: '',

    // 任务数据
    tasks: [],
    checkInStatus: null,
    
    // 分页
    pagination: {
      skip: 0,
      limit: 20,
      hasMore: true
    }
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('积分记录页面加载')
    this.loadPointsRecords()
    this.loadSourceStats()
    this.loadTasks()
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('积分记录页面显示')
    // 刷新数据
    this.refreshData()
    this.loadSourceStats()
    this.loadTasks()
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshData()
    wx.stopPullDownRefresh()
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.pagination.hasMore && !this.data.loading.more) {
      this.loadMoreRecords()
    }
  },

  /**
   * 刷新数据
   */
  refreshData() {
    this.setData({
      'pagination.skip': 0,
      'pagination.hasMore': true,
      records: []
    })
    this.loadPointsRecords()
  },

  /**
   * 加载积分记录
   */
  async loadPointsRecords() {
    try {
      this.setData({
        'loading.records': true
      })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getPointsRecords',
          data: {
            type: this.data.filter.type || undefined,
            limit: this.data.pagination.limit,
            skip: this.data.pagination.skip
          }
        }
      })

      if (result.result.success) {
        const data = result.result.data
        const newRecords = this.data.pagination.skip === 0 ? data.records : [...this.data.records, ...data.records]

        this.setData({
          records: newRecords,
          stats: data.stats || this.data.stats,
          'pagination.hasMore': data.records.length === this.data.pagination.limit
        })
      } else {
        wx.showToast({
          title: result.result.errMsg || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载积分记录失败:', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.records': false
      })
    }
  },

  /**
   * 加载更多记录
   */
  async loadMoreRecords() {
    this.setData({
      'loading.more': true,
      'pagination.skip': this.data.pagination.skip + this.data.pagination.limit
    })
    
    await this.loadPointsRecords()
    
    this.setData({
      'loading.more': false
    })
  },

  /**
   * 筛选类型变化
   */
  onFilterChange(event) {
    const { type } = event.currentTarget.dataset
    
    this.setData({
      'filter.type': type === this.data.filter.type ? '' : type
    })
    
    this.refreshData()
  },

  /**
   * 加载来源统计数据
   */
  async loadSourceStats() {
    try {
      this.setData({
        'loading.sourceStats': true
      })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getPointsStats',
          data: {}
        }
      })

      if (result.result.success) {
        const sourceStats = result.result.data.sourceStats
        this.processSourceStats(sourceStats)
      } else {
        console.error('获取来源统计失败:', result.result.errMsg)
      }
    } catch (error) {
      console.error('获取积分统计失败:', error)
    } finally {
      this.setData({
        'loading.sourceStats': false
      })
    }
  },

  /**
   * 处理来源统计数据
   */
  processSourceStats(sourceStats) {
    const sourceNames = {
      'check_in': '签到获得',
      'purchase': '商店消费',
      'admin': '管理员操作',
      'redeem': '兑换码使用',
      'invite_friend': '邀请好友',
      'invited_by_friend': '被邀请奖励'
    }

    const colors = [
      '#667eea',
      '#764ba2',
      '#f093fb',
      '#f5576c',
      '#4facfe',
      '#00f2fe'
    ]

    let totalPoints = 0
    const statsArray = []

    // 转换数据格式并计算总积分
    Object.keys(sourceStats).forEach((source, index) => {
      const stats = sourceStats[source]
      totalPoints += stats.totalPoints

      statsArray.push({
        source,
        name: sourceNames[source] || source,
        points: stats.totalPoints,
        count: stats.count,
        color: colors[index % colors.length]
      })
    })

    // 计算百分比
    statsArray.forEach(item => {
      item.percentage = totalPoints > 0 ? Math.round((item.points / totalPoints) * 100) : 0
    })

    // 按积分数量排序
    statsArray.sort((a, b) => b.points - a.points)

    // 生成饼图渐变
    const gradient = this.generatePieChartGradient(statsArray, totalPoints)

    this.setData({
      sourceStatsData: statsArray,
      totalSourcePoints: totalPoints,
      pieChartGradient: gradient
    })
  },

  /**
   * 生成饼图渐变
   */
  generatePieChartGradient(statsArray, totalPoints) {
    if (totalPoints === 0) return '#f5f5f5'

    let gradientParts = []
    let currentAngle = 0

    statsArray.forEach(item => {
      const percentage = (item.points / totalPoints) * 100
      const angle = (percentage / 100) * 360

      if (percentage > 0) {
        gradientParts.push(`${item.color} ${currentAngle}deg ${currentAngle + angle}deg`)
        currentAngle += angle
      }
    })

    return gradientParts.join(', ')
  },



  /**
   * 跳转到积分商店
   */
  onGoToStore() {
    wx.navigateTo({
      url: '/pages/store/index'
    })
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (days === 0) {
      const hours = Math.floor(diff / (1000 * 60 * 60))
      if (hours === 0) {
        const minutes = Math.floor(diff / (1000 * 60))
        return minutes <= 0 ? '刚刚' : `${minutes}分钟前`
      }
      return `${hours}小时前`
    } else if (days === 1) {
      return '昨天'
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  },

  /**
   * 获取来源图标
   */
  getSourceIcon(source) {
    const iconMap = {
      'check_in': '📅',
      'purchase': '🛒',
      'admin': '⚙️',
      'redeem': '🎫',
      'invite_friend': '👥',
      'invited_by_friend': '🎁'
    }
    return iconMap[source] || '💰'
  },

  /**
   * 获取来源名称
   */
  getSourceName(source) {
    const nameMap = {
      'check_in': '签到获得',
      'purchase': '商店消费',
      'admin': '管理员操作',
      'redeem': '兑换码使用',
      'invite_friend': '邀请好友',
      'invited_by_friend': '被邀请奖励'
    }
    return nameMap[source] || source
  },

  /**
   * 加载任务数据
   */
  async loadTasks() {
    try {
      // 获取签到状态
      await this.loadCheckInStatus()

      // 生成任务列表
      this.generateTasks()
    } catch (error) {
      console.error('加载任务失败:', error)
    }
  },

  /**
   * 获取签到状态
   */
  async loadCheckInStatus() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getCheckInStatus',
          data: {}
        }
      })

      if (result.result.success) {
        this.setData({
          checkInStatus: result.result.data
        })
      }
    } catch (error) {
      console.error('获取签到状态失败:', error)
    }
  },

  /**
   * 生成任务列表
   */
  generateTasks() {
    const checkInStatus = this.data.checkInStatus
    const tasks = [
      {
        id: 'daily_checkin',
        name: '每日签到',
        description: '每日签到获得积分，连续签到奖励更多',
        icon: '📅',
        iconColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        reward: checkInStatus ? checkInStatus.nextReward : 1,
        status: checkInStatus && checkInStatus.hasCheckedInToday ? 'completed' : 'available',
        actionText: checkInStatus && checkInStatus.hasCheckedInToday ? '已签到' : '去签到',
        progress: checkInStatus ? `连续签到${checkInStatus.consecutiveDays}天` : null
      },
      {
        id: 'invite_friends',
        name: '邀请好友',
        description: '邀请好友注册使用，双方都可获得积分',
        icon: '👥',
        iconColor: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        reward: 10,
        status: 'available',
        actionText: '邀请好友',
        progress: '今日已邀请0人'
      },
      {
        id: 'watch_ads',
        name: '观看广告',
        description: '观看完整广告视频获得积分奖励',
        icon: '📺',
        iconColor: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        reward: 2,
        status: 'available',
        actionText: '观看广告',
        progress: '今日已观看0/5次'
      },
      {
        id: 'share_app',
        name: '分享应用',
        description: '分享应用给好友，传播正能量',
        icon: '📤',
        iconColor: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        reward: 5,
        status: 'available',
        actionText: '分享应用',
        progress: '今日未分享'
      },
      {
        id: 'complete_profile',
        name: '完善资料',
        description: '完善个人资料信息，提升使用体验',
        icon: '👤',
        iconColor: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        reward: 20,
        status: 'available',
        actionText: '完善资料',
        progress: null
      }
    ]

    this.setData({
      tasks
    })
  },

  /**
   * 任务操作处理
   */
  async onTaskAction(event) {
    const taskId = event.currentTarget.dataset.task
    console.log('执行任务:', taskId)

    switch (taskId) {
      case 'daily_checkin':
        this.handleCheckInTask()
        break
      case 'invite_friends':
        this.handleInviteTask()
        break
      case 'watch_ads':
        this.handleWatchAdsTask()
        break
      case 'share_app':
        this.handleShareTask()
        break
      case 'complete_profile':
        this.handleProfileTask()
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  /**
   * 处理签到任务
   */
  handleCheckInTask() {
    wx.navigateTo({
      url: '/pages/check-in/index'
    })
  },

  /**
   * 处理邀请好友任务
   */
  handleInviteTask() {
    console.log('[INFO] 跳转到邀请好友页面')
    wx.navigateTo({
      url: '/pages/invite/index'
    })
  },

  /**
   * 处理观看广告任务
   */
  handleWatchAdsTask() {
    wx.showModal({
      title: '观看广告',
      content: '广告功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 处理分享任务
   */
  handleShareTask() {
    wx.showModal({
      title: '分享应用',
      content: '点击右上角菜单分享给好友，完成分享任务获得积分奖励！',
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 显示分享菜单
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline']
        })
      }
    })
  },

  /**
   * 处理完善资料任务
   */
  handleProfileTask() {
    wx.navigateTo({
      url: '/pages/profile/index'
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '积分记录 - 查看我的积分收支明细',
      path: '/pages/points/index',
      imageUrl: ''
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '积分记录 - 查看我的积分收支明细',
      query: '',
      imageUrl: ''
    }
  },

  /**
   * 获取类型样式类
   */
  getTypeClass(type) {
    return type === 'earn' ? 'type-earn' : 'type-spend'
  }
})
