/* 主入口页面样式 */
.main-container {
  min-height: 100vh;
  position: relative;
}

/* Index 页面导航栏 */
.index-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.index-navbar .status-bar {
  width: 100%;
  background: transparent;
}

.index-navbar .navbar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  padding: 0 32rpx;
}

.index-navbar .navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
}

/* 加载页面主题 */
.index-navbar.loading-theme {
  background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
}

/* 引导页面主题 */
.index-navbar.guide-theme {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

/* 加载遮罩层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f4fff7 0%, #d8e8f8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  padding-top: 132rpx; /* 为导航栏留出空间 */
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.loading-spinner {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 6rpx solid transparent;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
}

.spinner-ring:nth-child(1) {
  animation-delay: 0s;
  opacity: 1;
}

.spinner-ring:nth-child(2) {
  animation-delay: 0.4s;
  opacity: 0.7;
  transform: scale(0.8);
}

.spinner-ring:nth-child(3) {
  animation-delay: 0.8s;
  opacity: 0.4;
  transform: scale(0.6);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 500;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* 仪表盘容器 */
.dashboard-container {
  width: 100%;
  height: 100vh;
  position: relative;
  transition: opacity 0.3s ease-in-out;
}

.dashboard-container.transitioning {
  opacity: 0.3;
}

.dashboard-component {
  width: 100%;
  height: 100%;
  animation: fadeIn 0.5s ease-out;
}

/* 切换过渡遮罩 */
.transition-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  animation: fadeIn 0.2s ease-out;
}

.transition-spinner {
  display: flex;
  gap: 12rpx;
}

.spinner-dot {
  width: 16rpx;
  height: 16rpx;
  background: #667eea;
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.spinner-dot:nth-child(1) { animation-delay: -0.32s; }
.spinner-dot:nth-child(2) { animation-delay: -0.16s; }
.spinner-dot:nth-child(3) { animation-delay: 0s; }

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 引导遮罩层 */
.guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f4fff7 0%, #d8e8f8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  padding: 40rpx;
  padding-top: 180rpx; /* 为导航栏留出空间 */
  box-sizing: border-box;
}

/* 引导内容 */
.guide-content {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
  max-width: 600rpx;
  width: 100%;
  animation: slideUp 0.6s ease-out;
}

.guide-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.guide-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 24rpx;
  letter-spacing: 0.5rpx;
}

.guide-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 48rpx;
}

.guide-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.guide-btn:active {
  transform: scale(0.96);
}

.btn-icon {
  font-size: 28rpx;
}











/* 动画 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}


