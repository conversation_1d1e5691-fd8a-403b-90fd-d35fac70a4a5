/* 邀请好友页面样式 */
.invite-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 32rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.header-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 奖励卡片 */
.reward-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.reward-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.reward-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.reward-desc {
  font-size: 24rpx;
  color: #666;
}

.reward-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.reward-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.reward-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.reward-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.reward-amount {
  font-size: 32rpx;
  font-weight: 700;
  color: #28a745;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.reward-divider {
  font-size: 32rpx;
  font-weight: 700;
  color: #28a745;
  margin: 0 32rpx;
}

/* 统计卡片 */
.stats-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.stats-action {
  display: flex;
  align-items: center;
  color: #667eea;
  font-size: 24rpx;
}

.action-text {
  margin-right: 8rpx;
}

.action-arrow {
  font-size: 28rpx;
  font-weight: 700;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.stat-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 被邀请信息 */
.invited-by-info {
  padding: 24rpx;
  background: rgba(40, 167, 69, 0.1);
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}

.invited-by-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.invited-by-user {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 邀请用户列表 */
.invited-users {
  border-top: 2rpx solid #f5f5f5;
  padding-top: 24rpx;
}

.invited-users-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.invited-user-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
}

.invited-user-item:last-child {
  border-bottom: none;
}

.user-info {
  flex: 1;
  margin-left: 16rpx;
}

.invite-time {
  font-size: 20rpx;
  color: #999;
  margin-top: 4rpx;
}

.user-reward {
  font-size: 24rpx;
  font-weight: 700;
  color: #28a745;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* 操作卡片 */
.action-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.action-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.action-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.action-subtitle {
  font-size: 24rpx;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 24rpx;
  border-radius: 20rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #e9ecef;
}

.action-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  font-size: 36rpx;
  margin-bottom: 12rpx;
}

.btn-text {
  font-size: 28rpx;
}

.action-tip {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* 底部导航 */
.bottom-nav {
  display: flex;
  justify-content: center;
  margin-top: 32rpx;
}

.nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 48rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  border: none;
  color: #333;
  font-size: 24rpx;
  backdrop-filter: blur(10rpx);
}

.nav-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.nav-text {
  font-size: 24rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 0;
}

.loading-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}
