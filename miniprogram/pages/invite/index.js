// 邀请好友页面
Page({
  data: {
    // 邀请统计数据
    invitationStats: {
      totalInvited: 0,
      invitedUsers: [],
      invitedBy: null,
      rewards: {
        perInvitation: 10,
        totalEarned: 0
      }
    },
    
    // 加载状态
    loading: {
      stats: false,
      sharing: false
    },
    
    // 用户信息
    userInfo: null,
    
    // 分享配置
    shareConfig: {
      title: '邀请你一起使用时间跟踪器',
      desc: '高效管理工作时间，轻松追踪收入',
      imageUrl: ''
    }
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('[INFO] 邀请页面加载，参数:', options)

    // 检查是否通过邀请链接进入
    if (options.inviter) {
      console.log('[INFO] 检测到邀请参数:', options.inviter)
      this.handleInvitationLink(options.inviter)
    } else {
      // 检查是否有存储的邀请信息
      this.checkPendingInvitation()
    }

    this.loadInvitationStats()
    this.loadUserInfo()
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('[INFO] 邀请页面显示')
    this.loadInvitationStats()
  },

  /**
   * 检查存储的邀请信息
   */
  checkPendingInvitation() {
    try {
      const pendingInvitation = wx.getStorageSync('pendingInvitation')
      if (pendingInvitation && pendingInvitation.inviterUserId) {
        console.log('[INFO] 发现存储的邀请信息:', pendingInvitation)

        // 检查邀请信息是否过期（24小时）
        const now = Date.now()
        const invitationAge = now - pendingInvitation.timestamp
        const maxAge = 24 * 60 * 60 * 1000 // 24小时

        if (invitationAge < maxAge) {
          // 处理邀请
          this.handleInvitationLink(pendingInvitation.inviterUserId)

          // 清除存储的邀请信息
          wx.removeStorageSync('pendingInvitation')
        } else {
          console.log('[INFO] 邀请信息已过期，清除')
          wx.removeStorageSync('pendingInvitation')
        }
      }
    } catch (error) {
      console.error('[ERROR] 检查存储邀请信息失败:', error)
    }
  },

  /**
   * 处理邀请链接
   */
  async handleInvitationLink(inviterUserId) {
    try {
      console.log('[INFO] 处理邀请链接，邀请人ID:', inviterUserId)
      
      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'processInvitation',
          data: {
            inviterUserId: inviterUserId
          }
        }
      })

      console.log('[INFO] 邀请处理结果:', result.result)

      if (result.result.success) {
        const data = result.result.data
        
        // 显示邀请成功提示
        wx.showModal({
          title: '邀请成功！',
          content: `恭喜！您被 ${data.inviter.nickname} 邀请成功，双方都获得了积分奖励！\n\n您获得：${data.invitee.reward}积分\n邀请人获得：${data.inviter.reward}积分`,
          showCancel: false,
          confirmText: '太棒了！',
          success: () => {
            // 刷新邀请统计
            this.loadInvitationStats()
          }
        })
      } else {
        // 处理邀请失败的情况
        if (result.result.errMsg && !result.result.errMsg.includes('已经被')) {
          wx.showToast({
            title: result.result.errMsg,
            icon: 'none',
            duration: 3000
          })
        }
      }
    } catch (error) {
      console.error('[ERROR] 处理邀请链接失败:', error)
      wx.showToast({
        title: '处理邀请失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载邀请统计数据
   */
  async loadInvitationStats() {
    try {
      console.log('[INFO] 开始加载邀请统计数据')
      
      this.setData({
        'loading.stats': true
      })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getInvitationStats',
          data: {}
        }
      })

      console.log('[INFO] 邀请统计数据:', result.result)

      if (result.result.success) {
        this.setData({
          invitationStats: result.result.data
        })
      } else {
        console.error('[ERROR] 获取邀请统计失败:', result.result.errMsg)
        wx.showToast({
          title: result.result.errMsg || '获取数据失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('[ERROR] 加载邀请统计失败:', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.stats': false
      })
    }
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getUserInfo',
          data: {}
        }
      })

      if (result.result.success) {
        this.setData({
          userInfo: result.result.data
        })
        console.log('[INFO] 用户信息加载成功:', result.result.data._id)
      }
    } catch (error) {
      console.error('[ERROR] 加载用户信息失败:', error)
    }
  },

  /**
   * 邀请好友
   */
  onInviteFriends() {
    console.log('[INFO] 用户点击邀请好友')
    
    if (!this.data.userInfo) {
      wx.showToast({
        title: '用户信息加载中',
        icon: 'none'
      })
      return
    }

    // 显示分享提示
    wx.showModal({
      title: '邀请好友',
      content: '点击右上角菜单，选择"转发"分享给好友。好友通过您的分享进入小程序后，双方都将获得积分奖励！',
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 显示分享菜单
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline']
        })
      }
    })
  },

  /**
   * 复制邀请链接
   */
  onCopyInviteLink() {
    if (!this.data.userInfo) {
      wx.showToast({
        title: '用户信息加载中',
        icon: 'none'
      })
      return
    }

    const inviteUrl = `pages/invite/index?inviter=${this.data.userInfo._id}`
    
    wx.setClipboardData({
      data: inviteUrl,
      success: () => {
        wx.showToast({
          title: '邀请链接已复制',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 查看邀请规则
   */
  onViewRules() {
    wx.showModal({
      title: '邀请规则',
      content: '1. 分享小程序给好友，好友通过您的分享进入即可建立邀请关系\n\n2. 邀请成功后，您获得10积分，好友获得5积分\n\n3. 每个用户只能被邀请一次，不能重复获得奖励\n\n4. 不能邀请自己\n\n5. 积分奖励会立即发放到账户',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    console.log('[INFO] 用户分享邀请')
    
    if (!this.data.userInfo) {
      return {
        title: this.data.shareConfig.title,
        path: 'pages/invite/index'
      }
    }

    return {
      title: this.data.shareConfig.title,
      path: `pages/invite/index?inviter=${this.data.userInfo._id}`,
      imageUrl: this.data.shareConfig.imageUrl
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    console.log('[INFO] 用户分享到朋友圈')
    
    if (!this.data.userInfo) {
      return {
        title: this.data.shareConfig.title,
        query: ''
      }
    }

    return {
      title: this.data.shareConfig.title,
      query: `inviter=${this.data.userInfo._id}`,
      imageUrl: this.data.shareConfig.imageUrl
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('[INFO] 下拉刷新邀请数据')
    this.loadInvitationStats().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return ''

    const date = new Date(timestamp)
    const now = new Date()
    const diff = now - date

    // 小于1分钟
    if (diff < 60 * 1000) {
      return '刚刚'
    }

    // 小于1小时
    if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000))
      return `${minutes}分钟前`
    }

    // 小于1天
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000))
      return `${hours}小时前`
    }

    // 小于7天
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000))
      return `${days}天前`
    }

    // 超过7天，显示具体日期
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    if (year === now.getFullYear()) {
      return `${month}-${day}`
    } else {
      return `${year}-${month}-${day}`
    }
  },

  /**
   * 跳转到积分页面
   */
  onGoToPoints() {
    wx.navigateTo({
      url: '/pages/points/index'
    })
  }
})
