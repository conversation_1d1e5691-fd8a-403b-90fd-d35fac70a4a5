/**
 * 仪表盘配置服务
 * 基于全局数据管理器的仪表盘配置和切换服务
 */

/**
 * 仪表盘类型定义
 * 支持扩展更多仪表盘类型
 */
const DASHBOARD_TYPES = {
  DASHBOARD1: {
    id: 'dashboard1',
    name: '经典仪表盘',
    description: '传统的时间跟踪界面，功能全面',
    icon: '📊',
    component: 'dashboard1',
    isDefault: true,
    // 默认配置
    defaultConfig: {
      showRealTimeIncome: true,
      showQuickActions: true,
      chartHeight: 120,
      showCurrentWork: true
    }
  },
  DASHBOARD2: {
    id: 'dashboard2',
    name: '现代仪表盘',
    description: '现代化设计，卡片式布局',
    icon: '🎯',
    component: 'dashboard2',
    isDefault: false,
    // 默认配置
    defaultConfig: {
      showCountdown: true,
      showAllStats: true,
      circularProgressSize: 400,
      showCurrentWork: true
    }
  }
  // 未来可以在这里添加更多仪表盘类型
  // DASHBOARD3: {
  //   id: 'dashboard3',
  //   name: '未来仪表盘',
  //   description: '科技感十足的未来风格',
  //   icon: '🚀',
  //   component: 'dashboard3',
  //   isDefault: false,
  //   defaultConfig: {
  //     theme: 'dark',
  //     animations: true
  //   }
  // }
}

/**
 * 仪表盘服务类
 * 独立的仪表盘业务逻辑，但数据存储集成到 DataManager 中
 */
class DashboardService {
  constructor() {
    // 获取全局数据管理器实例
    this.dataManager = require('../managers/data-manager.js')
  }

  /**
   * 获取所有可用的仪表盘
   * @returns {Array} 仪表盘数组
   */
  getAllDashboards() {
    return Object.values(DASHBOARD_TYPES)
  }

  /**
   * 获取仪表盘信息
   * @param {string} dashboardId 仪表盘ID
   * @returns {Object|null} 仪表盘信息
   */
  getDashboardInfo(dashboardId) {
    const dashboardKey = Object.keys(DASHBOARD_TYPES).find(
      key => DASHBOARD_TYPES[key].id === dashboardId
    )
    return dashboardKey ? DASHBOARD_TYPES[dashboardKey] : null
  }

  /**
   * 获取当前仪表盘ID
   * @returns {string} 仪表盘ID
   */
  getCurrentDashboard() {
    return this.dataManager.getCurrentDashboard()
  }

  /**
   * 设置当前仪表盘
   * @param {string} dashboardId 仪表盘ID
   */
  setCurrentDashboard(dashboardId) {
    const dashboardInfo = this.getDashboardInfo(dashboardId)
    if (!dashboardInfo) {
      throw new Error('仪表盘不存在: ' + dashboardId)
    }

    this.dataManager.setCurrentDashboard(dashboardId)
    console.log('当前仪表盘已设置为:', dashboardId)
  }

  /**
   * 获取仪表盘设置
   * @param {string} dashboardId 仪表盘ID
   * @returns {Object} 仪表盘设置
   */
  getDashboardSettings(dashboardId) {
    const config = this.dataManager.getDashboardConfig(dashboardId)
    const dashboardInfo = this.getDashboardInfo(dashboardId)

    // 合并默认配置和用户配置
    return {
      ...(dashboardInfo?.defaultConfig || {}),
      ...config
    }
  }

  /**
   * 更新仪表盘设置（全局设置，如 showFloatingIcons）
   * @param {Object} settings 设置对象
   */
  updateDashboardSettings(settings) {
    this.dataManager.updateDashboardSettings(settings)
    console.log('仪表盘全局设置已更新:', settings)
  }

  /**
   * 更新特定仪表盘的配置
   * @param {string} dashboardId 仪表盘ID
   * @param {Object} config 配置对象
   */
  updateDashboardConfig(dashboardId, config) {
    const dashboardInfo = this.getDashboardInfo(dashboardId)
    if (!dashboardInfo) {
      throw new Error('仪表盘不存在: ' + dashboardId)
    }

    this.dataManager.updateDashboardConfig(dashboardId, config)
    console.log('仪表盘配置已更新:', dashboardId, config)
  }

  /**
   * 重置仪表盘设置
   * @param {string} dashboardId 仪表盘ID
   */
  resetDashboardSettings(dashboardId) {
    const dashboardInfo = this.getDashboardInfo(dashboardId)
    if (!dashboardInfo) {
      throw new Error('仪表盘不存在: ' + dashboardId)
    }

    // 重置为默认配置
    this.dataManager.updateDashboardConfig(dashboardId, dashboardInfo.defaultConfig || {})
    console.log('仪表盘设置已重置:', dashboardId)
  }

  /**
   * 检查是否首次使用
   * @returns {boolean} 是否首次使用
   */
  isFirstTime() {
    const settings = this.dataManager.getDashboardSettings()
    return settings.isFirstTime || false
  }

  /**
   * 标记首次使用完成
   */
  markFirstTimeComplete() {
    this.dataManager.updateDashboardSettings({
      isFirstTime: false
    })
    console.log('首次使用标记已完成')
  }

  /**
   * 添加新的仪表盘类型（扩展性支持）
   * @param {Object} dashboardType 仪表盘类型定义
   */
  addDashboardType(dashboardType) {
    if (!dashboardType.id || !dashboardType.name || !dashboardType.component) {
      throw new Error('仪表盘类型定义不完整')
    }

    // 动态添加到类型定义中
    const key = dashboardType.id.toUpperCase()
    DASHBOARD_TYPES[key] = {
      isDefault: false,
      defaultConfig: {},
      ...dashboardType
    }

    console.log('新仪表盘类型已添加:', dashboardType.id)
  }

  /**
   * 获取配置摘要
   * @returns {Object} 配置摘要
   */
  getConfigSummary() {
    const settings = this.dataManager.getDashboardSettings()
    return {
      currentDashboard: settings.currentDashboard,
      availableDashboards: this.getAllDashboards().map(d => d.id),
      isFirstTime: settings.isFirstTime,
      totalConfigs: Object.keys(settings.configs || {}).length
    }
  }

  /**
   * 添加数据变化监听器
   * @param {Function} listener 监听器函数
   */
  addChangeListener(listener) {
    this.dataManager.addChangeListener(listener)
  }

  /**
   * 移除数据变化监听器
   * @param {Function} listener 监听器函数
   */
  removeChangeListener(listener) {
    this.dataManager.removeChangeListener(listener)
  }
}

// 导出服务类和常量
module.exports = {
  DashboardService,
  DASHBOARD_TYPES
}
