/**
 * 节假日数据管理器
 * 负责获取、缓存和查询节假日信息
 * 
 * 功能特性：
 * - 每日自动更新缓存
 * - 本地存储管理
 * - 快速查询接口
 * - 内存缓存优化
 */

class HolidayManager {
  constructor() {
    // 单例模式
    if (HolidayManager.instance) {
      return HolidayManager.instance
    }

    // 缓存键名
    this.storageKey = 'HOLIDAY_DATA_CACHE'
    this.lastUpdateKey = 'HOLIDAY_LAST_UPDATE'
    
    // 内存缓存
    this.holidayCache = new Map()
    this.weekendCache = new Map()
    
    // 缓存状态
    this.isLoading = false
    this.loadPromise = null
    
    HolidayManager.instance = this
  }

  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!HolidayManager.instance) {
      HolidayManager.instance = new HolidayManager()
    }
    return HolidayManager.instance
  }

  /**
   * 初始化节假日数据
   * 检查缓存是否过期，如果过期则从云端更新
   */
  async initialize() {
    if (this.isLoading) {
      return this.loadPromise
    }

    this.isLoading = true
    this.loadPromise = this._doInitialize()
    
    try {
      await this.loadPromise
    } finally {
      this.isLoading = false
      this.loadPromise = null
    }
  }

  /**
   * 执行初始化逻辑
   */
  async _doInitialize() {
    try {
      // 先加载本地缓存数据
      await this._loadFromLocalStorage()
      
      // 检查是否需要更新
      const needUpdate = this._shouldUpdateCache()
      
      if (needUpdate) {
        console.log('节假日缓存过期，开始更新...')
        await this._updateFromCloud()
      } else {
        console.log('节假日缓存有效，使用本地数据')
      }
    } catch (error) {
      console.error('初始化节假日数据失败:', error)
      // 如果云端更新失败，使用本地缓存数据
    }
  }

  /**
   * 检查是否需要更新缓存
   */
  _shouldUpdateCache() {
    try {
      const lastUpdate = wx.getStorageSync(this.lastUpdateKey)
      if (!lastUpdate) {
        return true // 从未更新过
      }

      const lastUpdateDate = new Date(lastUpdate)
      const today = new Date()
      
      // 检查是否是新的一天
      const todayDateString = today.toDateString()
      const lastUpdateDateString = lastUpdateDate.toDateString()
      
      return todayDateString !== lastUpdateDateString
    } catch (error) {
      console.error('检查缓存更新状态失败:', error)
      return true // 出错时强制更新
    }
  }

  /**
   * 从本地存储加载数据
   */
  async _loadFromLocalStorage() {
    try {
      const cachedData = wx.getStorageSync(this.storageKey)
      if (cachedData && cachedData.holidays) {
        // 将数据加载到内存缓存
        this._buildMemoryCache(cachedData.holidays)
        console.log('从本地存储加载节假日数据成功')
      }
    } catch (error) {
      console.error('从本地存储加载节假日数据失败:', error)
    }
  }

  /**
   * 从云端更新数据
   */
  async _updateFromCloud() {
    try {
      const currentYear = new Date().getFullYear()
      const previousYear = currentYear - 1
      const nextYear = currentYear + 1
      
      // 获取当前年、上一年和下一年的节假日数据
      const years = [previousYear, currentYear, nextYear]
      const allHolidays = []
      
      for (const year of years) {
        try {
          const response = await this._callCloudFunction('getHolidayData', { year })
          console.log(`获取${year}年节假日数据:`, response)
          
          const result = response.result || response
          if (result.success && result.data && result.data.holidays) {
            allHolidays.push(...result.data.holidays)
            console.log(`${year}年节假日数据获取成功，共${result.data.holidays.length}条`)
          } else {
            console.warn(`${year}年节假日数据格式错误:`, result)
          }
        } catch (error) {
          console.warn(`获取${year}年节假日数据失败:`, error)
        }
      }
      
      if (allHolidays.length > 0) {
        // 保存到本地存储
        const cacheData = {
          holidays: allHolidays,
          lastUpdate: new Date().toISOString(),
          version: '1.0.0'
        }
        
        await this._saveToLocalStorage(cacheData)
        
        // 更新内存缓存
        this._buildMemoryCache(allHolidays)
        
        // 记录更新时间
        wx.setStorageSync(this.lastUpdateKey, new Date().toISOString())
        
        console.log(`节假日数据更新成功，共${allHolidays.length}条记录`)
      }
    } catch (error) {
      console.error('从云端更新节假日数据失败:', error)
      throw error
    }
  }

  /**
   * 保存数据到本地存储
   */
  async _saveToLocalStorage(data) {
    try {
      // 清理旧数据
      await this._cleanOldCache()
      
      // 保存新数据
      wx.setStorageSync(this.storageKey, data)
    } catch (error) {
      console.error('保存节假日数据到本地存储失败:', error)
      throw error
    }
  }

  /**
   * 清理旧的缓存数据
   */
  async _cleanOldCache() {
    try {
      // 获取所有存储的键名
      const info = wx.getStorageInfoSync()
      const keys = info.keys || []
      
      // 清理过期的节假日缓存
      const holidayKeys = keys.filter(key => 
        key.startsWith('HOLIDAY_') && key !== this.storageKey && key !== this.lastUpdateKey
      )
      
      for (const key of holidayKeys) {
        wx.removeStorageSync(key)
      }
      
      console.log(`清理了${holidayKeys.length}个过期的节假日缓存`)
    } catch (error) {
      console.error('清理旧缓存失败:', error)
    }
  }

  /**
   * 构建内存缓存
   */
  _buildMemoryCache(holidays) {
    this.holidayCache.clear()
    
    console.log('构建节假日内存缓存，数据量:', holidays.length)
    
    for (const holiday of holidays) {
      const dateKey = holiday.date
      if (!this.holidayCache.has(dateKey)) {
        this.holidayCache.set(dateKey, [])
      }
      this.holidayCache.get(dateKey).push(holiday)
    }
    
    console.log('节假日内存缓存构建完成，缓存大小:', this.holidayCache.size)
    
    // 打印一些缓存的例子用于调试
    let sampleCount = 0
    for (const [dateKey, holidayList] of this.holidayCache.entries()) {
      if (sampleCount < 5) {
        console.log(`缓存示例 ${dateKey}:`, holidayList)
        sampleCount++
      }
    }
  }

  /**
   * 调用云函数
   */
  async _callCloudFunction(functionName, data) {
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: functionName,
          ...data
        },
        success: resolve,
        fail: reject
      })
    })
  }

  /**
   * 获取指定日期的节假日信息
   * @param {Date|string} date - 日期对象或日期字符串
   * @returns {Object|null} 节假日信息
   */
  getHolidayInfo(date) {
    const dateStr = this._formatDateString(date)
    const holidays = this.holidayCache.get(dateStr)
    
    // 调试日志
    if (dateStr === '2025-01-01' || dateStr === '2025-01-28') {
      console.log(`查询节假日 ${dateStr}:`, holidays)
      console.log('当前缓存大小:', this.holidayCache.size)
    }
    
    if (holidays && holidays.length > 0) {
      return holidays[0] // 返回第一个匹配的节假日信息
    }
    
    return null
  }

  /**
   * 检查指定日期是否为节假日
   * @param {Date|string} date - 日期
   * @returns {boolean} 是否为节假日
   */
  isHoliday(date) {
    const holidayInfo = this.getHolidayInfo(date)
    return holidayInfo !== null && holidayInfo.type === 'holiday'
  }

  /**
   * 检查指定日期是否为调休工作日
   * @param {Date|string} date - 日期
   * @returns {boolean} 是否为调休工作日
   */
  isWorkday(date) {
    const holidayInfo = this.getHolidayInfo(date)
    return holidayInfo !== null && holidayInfo.type === 'workday'
  }

  /**
   * 检查指定日期是否为周末
   * @param {Date|string} date - 日期
   * @returns {boolean} 是否为周末
   */
  isWeekend(date) {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const dayOfWeek = dateObj.getDay()
    return dayOfWeek === 0 || dayOfWeek === 6
  }

  /**
   * 获取指定日期的完整信息
   * @param {Date|string} date - 日期
   * @returns {Object} 日期信息
   */
  getDateInfo(date) {
    const holidayInfo = this.getHolidayInfo(date)
    const isWeekend = this.isWeekend(date)

    let type = 'normal' // 普通工作日
    let name = ''
    let isWork = true

    if (holidayInfo) {
      type = holidayInfo.type
      name = holidayInfo.name
      isWork = holidayInfo.isWork
    } else if (isWeekend) {
      type = 'weekend'
      const dateObj = typeof date === 'string' ? new Date(date) : date
      name = dateObj.getDay() === 0 ? '周日' : '周六'
      isWork = false
    }

    return {
      date: this._formatDateString(date),
      type,
      name,
      isWork,
      isHoliday: type === 'holiday',
      isWeekend: type === 'weekend',
      isWorkday: type === 'workday'
    }
  }

  /**
   * 获取指定月份的所有节假日
   * @param {number} year - 年份
   * @param {number} month - 月份（1-12）
   * @returns {Array} 节假日列表
   */
  getMonthHolidays(year, month) {
    const holidays = []
    const daysInMonth = new Date(year, month, 0).getDate()

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day)
      const dateInfo = this.getDateInfo(date)

      if (dateInfo.type !== 'normal') {
        holidays.push(dateInfo)
      }
    }

    return holidays
  }

  /**
   * 强制更新节假日数据
   */
  async forceUpdate() {
    try {
      // 清除本地缓存
      wx.removeStorageSync(this.storageKey)
      wx.removeStorageSync(this.lastUpdateKey)
      this.holidayCache.clear()

      // 重新初始化
      await this.initialize()

      console.log('节假日数据强制更新完成')
    } catch (error) {
      console.error('强制更新节假日数据失败:', error)
      throw error
    }
  }

  /**
   * 格式化日期字符串
   * @param {Date|string} date - 日期
   * @returns {string} YYYY-MM-DD 格式的日期字符串
   */
  _formatDateString(date) {
    if (typeof date === 'string') {
      return date.split('T')[0] // 移除时间部分
    }

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    return `${year}-${month}-${day}`
  }

  /**
   * 获取缓存信息
   */
  getCacheInfo() {
    try {
      const cacheData = wx.getStorageSync(this.storageKey)
      const lastUpdate = wx.getStorageSync(this.lastUpdateKey)

      return {
        hasCache: !!cacheData,
        lastUpdate: lastUpdate || null,
        cacheSize: this.holidayCache.size,
        version: cacheData ? cacheData.version : null
      }
    } catch (error) {
      console.error('获取缓存信息失败:', error)
      return {
        hasCache: false,
        lastUpdate: null,
        cacheSize: 0,
        version: null
      }
    }
  }

  /**
   * 调试方法：打印节假日管理器状态
   */
  debugStatus() {
    console.log('=== 节假日管理器状态 ===')
    console.log('内存缓存大小:', this.holidayCache.size)
    console.log('缓存信息:', this.getCacheInfo())

    // 测试几个已知的节假日
    const testDates = ['2025-01-01', '2025-01-28', '2025-02-01', '2025-05-01']
    testDates.forEach(dateStr => {
      const info = this.getDateInfo(dateStr)
      console.log(`${dateStr}:`, info)
    })

    // 测试周末
    const weekendDate = '2025-01-04' // 这应该是周六
    console.log(`${weekendDate} (周末测试):`, this.getDateInfo(weekendDate))

    console.log('=== 状态检查完成 ===')
  }
}

// 导出单例实例
let holidayManagerInstance = null

function getHolidayManager() {
  if (!holidayManagerInstance) {
    holidayManagerInstance = new HolidayManager()
  }
  return holidayManagerInstance
}

module.exports = {
  HolidayManager,
  getHolidayManager
}
