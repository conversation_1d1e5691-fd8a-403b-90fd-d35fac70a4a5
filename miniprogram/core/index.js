/**
 * 核心模块统一导出文件
 * 提供所有核心管理器和服务的统一入口
 */

// 导入所有模块
const dataManager = require('./managers/data-manager.js')
const userManager = require('./managers/user-manager.js')
const syncManager = require('./managers/sync-manager.js')
const { DashboardService } = require('./services/dashboard-service.js')
const { WorkHistoryService } = require('./services/work-history-service.js')
const { TimeSegmentService } = require('./services/time-segment-service.js')
const { DashboardBaseService } = require('./services/dashboard-base-service.js')
const { DataImportExportService } = require('./services/data-import-export-service.js')
const { StatisticsService } = require('./services/statistics-service.js')

// 统一导出
module.exports = {
  // 数据管理器
  dataManager,
  userManager,
  syncManager,

  // 服务
  DashboardService,
  WorkHistoryService,
  TimeSegmentService,
  DashboardBaseService,
  DataImportExportService,
  StatisticsService
}
