// 应用程序入口文件
const dataManager = require('./core/managers/data-manager.js')
const userManager = require('./core/managers/user-manager.js')
const syncManager = require('./core/managers/sync-manager.js')
const { getHolidayManager } = require('./core/managers/holiday-manager.js')

App({
  /**
   * 应用启动时触发
   */
  onLaunch() {
    console.log('应用启动')

    // 初始化云开发环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        // 使用默认环境，或者指定具体的环境ID
        traceUser: true,
      })
      console.log('云开发环境初始化完成')
    }

    // 异步初始化管理器，避免阻塞应用启动
    this.initializeManagers()
  },

  /**
   * 应用显示时触发（从后台切回前台）
   */
  onShow(options) {
    console.log('应用显示，启动参数:', options)

    // 确保用户信息已加载
    this.ensureUserInfo()

    // 执行启动时数据同步检查
    this.checkDataSync()

    // 处理邀请链接（如果有）
    this.handleInvitationFromOptions(options)
  },

  /**
   * 初始化所有管理器
   */
  async initializeManagers() {
    try {
      console.log('开始初始化管理器...')

      // 1. 初始化全局数据管理器
      await this.initDataManager()

      // 2. 初始化用户管理器
      await this.initUserManager()

      // 3. 初始化同步管理器
      await this.initSyncManager()

      // 4. 初始化节假日管理器
      await this.initHolidayManager()

      console.log('所有管理器初始化完成')
    } catch (error) {
      console.error('管理器初始化失败:', error)
    }
  },

  /**
   * 初始化数据管理器
   */
  async initDataManager() {
    const startTime = Date.now()

    try {
      console.log('初始化全局数据管理器...')

      // 确保数据管理器已加载
      await dataManager.ensureLoaded()

      // 检查并处理过期的摸鱼状态
      this.checkExpiredFishing()

      const endTime = Date.now()
      console.log(`数据管理器初始化完成，耗时: ${endTime - startTime}ms`)

      // 添加全局错误监听
      dataManager.addChangeListener(() => {
        console.log('数据已更新')
      })

    } catch (error) {
      console.error('数据管理器初始化失败:', error)

      // 尝试使用默认数据
      try {
        console.log('尝试使用默认数据初始化...')
        dataManager.initializeData()
        console.log('使用默认数据初始化成功')
      } catch (fallbackError) {
        console.error('默认数据初始化也失败:', fallbackError)
      }
    }
  },

  /**
   * 检查过期的摸鱼状态
   */
  checkExpiredFishing() {
    try {
      console.log('检查过期的摸鱼状态...')

      const result = dataManager.checkAndHandleExpiredFishing()

      if (result.hasExpiredFishing) {
        if (result.autoEnded) {
          console.log('检测到过期摸鱼状态，已自动结束:', result.message)

          // 显示提示信息
          setTimeout(() => {
            wx.showToast({
              title: result.message,
              icon: 'none',
              duration: 3000
            })
          }, 1000) // 延迟1秒显示，确保页面已加载
        } else {
          console.log('检测到活跃的摸鱼状态，继续摸鱼')
        }
      }
    } catch (error) {
      console.error('检查摸鱼状态失败:', error)
    }
  },

  /**
   * 初始化用户管理器
   */
  async initUserManager() {
    try {
      console.log('初始化用户管理器...')
      await userManager.initialize()
      console.log('用户管理器初始化完成')
    } catch (error) {
      console.error('用户管理器初始化失败:', error)
    }
  },

  /**
   * 确保用户信息已加载
   */
  async ensureUserInfo() {
    try {
      if (!userManager.isLoggedIn) {
        console.log('用户未登录，重新获取用户信息...')
        await userManager.initialize()
      }
    } catch (error) {
      console.error('确保用户信息失败:', error)
    }
  },

  /**
   * 检查数据同步
   */
  async checkDataSync() {
    try {
      // 延迟执行，确保所有管理器都已初始化
      setTimeout(async () => {
        if (!syncManager.isInitialized) {
          console.log('同步管理器未初始化，跳过数据同步检查')
          return
        }

        // 执行启动时数据同步检查
        await syncManager.checkAndSyncOnStartup()
      }, 2000) // 延迟2秒执行，确保初始化完成
    } catch (error) {
      console.error('数据同步检查失败:', error)
    }
  },



  /**
   * 初始化同步管理器
   */
  async initSyncManager() {
    try {
      console.log('初始化同步管理器...')
      await syncManager.initialize({
        dataManager: dataManager,
        userManager: userManager
      })
      console.log('同步管理器初始化完成')
    } catch (error) {
      console.error('同步管理器初始化失败:', error)
    }
  },

  /**
   * 初始化节假日管理器
   */
  async initHolidayManager() {
    try {
      console.log('初始化节假日管理器...')
      const holidayManager = getHolidayManager()
      await holidayManager.initialize()
      console.log('节假日管理器初始化完成')
    } catch (error) {
      console.error('节假日管理器初始化失败:', error)
    }
  },

  /**
   * 获取全局数据管理器
   * @returns {DataManager} 数据管理器实例
   */
  getDataManager() {
    return dataManager
  },

  /**
   * 获取用户管理器
   * @returns {UserManager} 用户管理器实例
   */
  getUserManager() {
    return userManager
  },



  /**
   * 获取同步管理器
   * @returns {SyncManager} 同步管理器实例
   */
  getSyncManager() {
    return syncManager
  },

  /**
   * 获取节假日管理器
   * @returns {HolidayManager} 节假日管理器实例
   */
  getHolidayManager() {
    return getHolidayManager()
  },

  /**
   * 应用隐藏时触发
   */
  onHide() {
    console.log('应用隐藏')
    // 应用隐藏时摸鱼状态继续，由实时收入计算器负责检查
  },

  /**
   * 应用卸载时触发（很少触发）
   */
  onUnload() {
    console.log('应用卸载')
    // 清理摸鱼管理器资源
    if (dataManager && dataManager.fishingManager) {
      dataManager.fishingManager.cleanup()
    }
  },

  /**
   * 处理邀请链接
   */
  handleInvitationFromOptions(options) {
    if (!options || !options.query) {
      return
    }

    const { inviter } = options.query
    if (inviter) {
      console.log('[INFO] 检测到邀请参数，邀请人ID:', inviter)

      // 存储邀请信息，等待用户登录后处理
      wx.setStorageSync('pendingInvitation', {
        inviterUserId: inviter,
        timestamp: Date.now()
      })

      // 如果当前页面不是邀请页面，跳转到邀请页面
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      if (currentPage && currentPage.route !== 'pages/invite/index') {
        wx.navigateTo({
          url: `/pages/invite/index?inviter=${inviter}`,
          fail: () => {
            // 如果跳转失败，可能是因为页面栈满了，使用redirectTo
            wx.redirectTo({
              url: `/pages/invite/index?inviter=${inviter}`
            })
          }
        })
      }
    }
  },

  /**
   * 全局数据
   */
  globalData: {
    // 保留用于其他全局数据
  }
})
