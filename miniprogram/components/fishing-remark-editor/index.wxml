<!--摸鱼备注编辑器-->
<view class="fishing-remark-editor" wx:if="{{show}}">
  <!-- 遮罩层 -->
  <view class="editor-mask" bindtap="onClose"></view>
  
  <!-- 编辑器内容 -->
  <view class="editor-content" bindtap="onStopPropagation">
    <!-- 标题栏 -->
    <view class="editor-header">
      <text class="editor-title">编辑摸鱼备注</text>
      <view class="close-btn" bindtap="onClose">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <!-- 编辑器主体 -->
    <view class="editor-body">
      <!-- 快捷备注 -->
      <view class="quick-remarks-section" wx:if="{{quickRemarks.length > 0}}">
        <view class="section-title">
          <text class="title-icon">⚡</text>
          <text class="title-text">快捷备注</text>
        </view>
        
        <view class="quick-remarks-grid">
          <view 
            class="quick-remark-item"
            wx:for="{{quickRemarks}}"
            wx:key="remark"
            data-remark="{{item.remark}}"
            bindtap="onSelectQuickRemark">
            <text class="remark-text">{{item.remark}}</text>
            <text class="remark-count">{{item.count}}次</text>
          </view>
        </view>
      </view>

      <!-- 无快捷备注提示 -->
      <view class="no-quick-remarks" wx:if="{{quickRemarks.length === 0}}">
        <view class="section-title">
          <text class="title-icon">⚡</text>
          <text class="title-text">快捷备注</text>
        </view>
        <view class="no-remarks-tip">
          <text class="tip-icon">💡</text>
          <text class="tip-text">暂无历史备注，添加备注后会在这里显示常用选项</text>
        </view>
      </view>

      <!-- 自定义备注输入 -->
      <view class="custom-remark-section">
        <view class="section-title">
          <text class="title-icon">✏️</text>
          <text class="title-text">自定义备注</text>
        </view>
        
        <textarea 
          class="remark-textarea"
          placeholder="输入摸鱼备注..."
          value="{{remarkInput}}"
          maxlength="100"
          bindinput="onRemarkInput"
          auto-focus="{{true}}"
        ></textarea>
        
        <view class="char-count">{{remarkInput.length}}/100</view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="editor-footer">
      <button 
        class="editor-btn cancel-btn" 
        bindtap="onCancel"
      >
        取消
      </button>
      <button 
        class="editor-btn save-btn" 
        disabled="{{loading}}"
        bindtap="onSaveRemark"
      >
        {{loading ? '保存中...' : '保存'}}
      </button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-overlay" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
    </view>
  </view>
</view>
