/**
 * 摸鱼备注编辑器组件
 * 支持快捷备注选择和自定义备注输入
 */

const dataManager = require('../../core/managers/data-manager.js')
const { DataCalculator } = require('../../utils/helpers/data-calculator.js')

Component({
  options: {
    addGlobalClass: true
  },

  properties: {
    // 是否显示编辑器
    show: {
      type: Boolean,
      value: false
    },
    // 当前备注
    currentRemark: {
      type: String,
      value: ''
    },
    // 备注内容（兼容性属性）
    remark: {
      type: String,
      value: ''
    }
  },

  data: {
    // 输入的备注
    remarkInput: '',
    // 快捷备注列表
    quickRemarks: [],
    // 加载状态
    loading: false
  },

  observers: {
    'show': function(show) {
      if (show) {
        this.initEditor()
      }
    }
  },

  methods: {
    /**
     * 初始化编辑器
     */
    initEditor() {
      // 支持两种属性名
      const currentRemark = this.data.currentRemark || this.data.remark || ''
      this.setData({
        remarkInput: currentRemark
      })
      this.loadQuickRemarks()
    },

    /**
     * 加载快捷备注
     */
    loadQuickRemarks() {
      try {
        const userData = dataManager.getUserData()
        const quickRemarks = DataCalculator.getQuickRemarksFromHistory(userData)

        this.setData({
          quickRemarks: quickRemarks
        })
      } catch (error) {
        console.error('加载快捷备注失败:', error)
      }
    },

    /**
     * 备注输入
     */
    onRemarkInput(e) {
      this.setData({
        remarkInput: e.detail.value
      })
    },

    /**
     * 选择快捷备注
     */
    onSelectQuickRemark(e) {
      const remark = e.currentTarget.dataset.remark
      this.saveRemark(remark)
    },

    /**
     * 保存备注
     */
    onSaveRemark() {
      const remark = this.data.remarkInput.trim()
      this.saveRemark(remark)
    },

    /**
     * 执行保存备注
     * @param {string} remark - 备注内容
     */
    saveRemark(remark) {
      if (this.data.loading) return

      this.setData({ loading: true })

      try {
        const result = dataManager.updateCurrentFishingRemark(remark)

        if (result.success) {
          // 触发保存事件（支持多种事件名）
          this.triggerEvent('save', {
            remark: remark
          })
          this.triggerEvent('confirm', {
            remark: remark
          })

          // 关闭编辑器
          this.onClose()

          wx.showToast({
            title: '备注已保存',
            icon: 'success'
          })
        } else {
          throw new Error(result.message || '保存失败')
        }
      } catch (error) {
        console.error('保存备注失败:', error)
        wx.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.setData({ loading: false })
      }
    },

    /**
     * 取消编辑
     */
    onCancel() {
      this.triggerEvent('cancel')
      this.onClose()
    },

    /**
     * 关闭编辑器
     */
    onClose() {
      this.setData({
        remarkInput: '',
        quickRemarks: []
      })
      this.triggerEvent('close')
    },

    /**
     * 阻止冒泡
     */
    onStopPropagation() {
      // 阻止事件冒泡
    }
  }
})
