/* 摸鱼控制组件样式 */

.fishing-control {
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.fishing-control.compact {
  padding: 16rpx;
  border-radius: 12rpx;
}

/* 摸鱼状态显示 */
.fishing-status {
  margin-bottom: 24rpx;
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.edit-remark-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  margin-left: auto;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.edit-remark-btn:active {
  background: rgba(255, 255, 255, 1);
  transform: scale(0.95);
}

.edit-icon {
  font-size: 24rpx;
}

.status-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
}

.fishing-duration {
  font-size: 48rpx;
  font-weight: 700;
  color: #1890ff;
  text-align: center;
  font-family: 'Courier New', monospace;
  letter-spacing: 2rpx;
  margin-bottom: 12rpx;
}

.fishing-remark {
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 12rpx;
  margin-top: 12rpx;
}

.remark-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.remark-text {
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
}

/* 控制按钮 */
.fishing-controls {
  display: flex;
  justify-content: center;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  min-width: 200rpx;
  transition: all 0.3s ease;
}

.control-btn:disabled {
  opacity: 0.6;
}

.start-btn {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: #fff;
}

.start-btn:not(:disabled):active {
  background: linear-gradient(135deg, #096dd9, #1890ff);
  transform: scale(0.98);
}

.end-btn {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  color: #fff;
}

.end-btn:not(:disabled):active {
  background: linear-gradient(135deg, #d9363e, #ff4d4f);
  transform: scale(0.98);
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.btn-text {
  font-size: 28rpx;
}

/* 备注输入弹窗 */
.remark-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  padding: 32rpx 32rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-body {
  padding: 24rpx 32rpx;
}

.input-group {
  margin-bottom: 24rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.remark-input {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
}

.remark-input:focus {
  border-color: #1890ff;
  outline: none;
}

.input-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.modal-footer {
  display: flex;
  padding: 16rpx 32rpx 32rpx;
  gap: 16rpx;
}

.modal-btn {
  flex: 1;
  padding: 16rpx;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background: #e6e6e6;
}

.confirm-btn {
  background: #1890ff;
  color: #fff;
}

.confirm-btn:not(:disabled):active {
  background: #096dd9;
}

.confirm-btn:disabled {
  background: #d9d9d9;
  color: #999;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 紧凑模式样式调整 */
.fishing-control.compact .fishing-status {
  margin-bottom: 16rpx;
}

.fishing-control.compact .fishing-duration {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.fishing-control.compact .control-btn {
  padding: 12rpx 24rpx;
  min-width: 160rpx;
  font-size: 26rpx;
}

.fishing-control.compact .btn-icon {
  font-size: 28rpx;
}

.fishing-control.compact .btn-text {
  font-size: 26rpx;
}
