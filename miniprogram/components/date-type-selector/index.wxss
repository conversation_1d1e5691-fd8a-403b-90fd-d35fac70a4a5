/* 日期类型选择器样式 */

.date-type-selector {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.selector-content {
  background: #ffffff;
  border-radius: 24rpx;
  width: 100%;
  max-width: 680rpx;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

/* 模态框头部 */
.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafafa;
  border-radius: 24rpx 24rpx 0 0;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.header-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f3f4f6;
  transition: all 0.2s ease;
}

.header-close:active {
  background: #e5e7eb;
  transform: scale(0.95);
}

.close-icon {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: bold;
}

/* 分类容器 */
.categories-container {
  flex: 1;
  overflow-y: auto;
  padding: 24rpx 24rpx 0;
}

/* 分类区域 */
.category-section {
  margin-bottom: 40rpx;
}

.category-section:last-child {
  margin-bottom: 0;
}

/* 分类标题 */
.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.category-description {
  font-size: 24rpx;
  color: #6b7280;
}

/* 类型网格 */
.types-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}

/* 类型项 */
.type-item {
  position: relative;
  background: transparent;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  padding: 16rpx 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.2s ease;
  min-height: 100rpx;
  justify-content: center;
}

/* 启用状态 */
.type-enabled {
  cursor: pointer;
}

.type-enabled:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 选中状态 */
.type-selected {
  background: var(--type-bg-color, #f9fafb) !important;
  border-color: var(--type-color, #3b82f6) !important;
  border-width: 3rpx !important;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
}

/* 禁用状态 */
.type-disabled {
  opacity: 0.6;
}

.type-icon {
  font-size: 28rpx;
  margin-bottom: 6rpx;
}

.type-text {
  font-size: 22rpx;
  color: #374151;
  font-weight: 500;
  line-height: 1.1;
  text-align: center;
}

.type-selected .type-text {
  color: var(--type-color, #374151);
  font-weight: 600;
}

.type-disabled .type-text {
  color: #9ca3af;
}

/* 禁用遮罩 */
.type-disabled-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.disabled-text {
  font-size: 18rpx;
  color: #9ca3af;
  font-weight: 500;
}

/* 选中指示器 */
.type-selected-indicator {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 24rpx;
  height: 24rpx;
  background: var(--type-color, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-icon {
  color: #ffffff;
  font-size: 16rpx;
  font-weight: bold;
}

/* 操作按钮 */
.selector-actions {
  display: flex;
  padding: 24rpx 32rpx;
  gap: 16rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
  border-radius: 0 0 24rpx 24rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.98);
}

.cancel-btn {
  background: #f3f4f6;
  color: #6b7280;
  border: 2rpx solid #e5e7eb;
}

.cancel-btn:active {
  background: #e5e7eb;
}

.confirm-btn {
  background: #3b82f6;
  color: #ffffff;
  border: 2rpx solid #3b82f6;
}

.confirm-btn:active {
  background: #2563eb;
  border-color: #2563eb;
}
