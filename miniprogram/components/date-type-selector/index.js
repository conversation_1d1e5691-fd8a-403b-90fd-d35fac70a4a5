/**
 * 日期类型选择器组件
 * 支持分类展示和选择日期状态类型
 */

const { TimeSegmentService } = require('../../core/services/time-segment-service.js')

Component({
  options: {
    addGlobalClass: true
  },

  properties: {
    // 是否显示选择器
    show: {
      type: Boolean,
      value: false
    },
    // 当前选中的状态值
    value: {
      type: String,
      value: 'none'
    },
    // 标题
    title: {
      type: String,
      value: '选择日期类型'
    }
  },

  data: {
    // 日期状态分类数据
    categories: [],
    // 当前选中的状态
    selectedValue: 'none'
  },

  lifetimes: {
    attached() {
      console.log('日期类型选择器组件已加载')

      // 初始化服务
      this.timeSegmentService = new TimeSegmentService()

      this.loadCategories()
    }
  },

  observers: {
    'value': function(newValue) {
      console.log('日期类型选择器 - 监听到value变化:', newValue)
      this.setData({
        selectedValue: newValue || 'none'
      })
    },

    'show': function(newShow) {
      console.log('日期类型选择器 - 显示状态变化:', newShow)
      if (newShow) {
        this.loadCategories()
      }
    }
  },

  methods: {
    /**
     * 加载分类数据
     */
    loadCategories() {
      try {
        // 确保服务已初始化
        if (!this.timeSegmentService) {
          console.error('日期类型选择器 - 时间段服务未初始化')
          return
        }

        // 获取分类数据
        const categories = this.timeSegmentService.getDateStatusCategories()
        console.log('日期类型选择器 - 加载分类数据:', categories)

        this.setData({
          categories: categories
        })
      } catch (error) {
        console.error('日期类型选择器 - 加载分类数据失败:', error)
      }
    },

    /**
     * 选择日期类型
     */
    onSelectType(e) {
      const { value, enabled } = e.currentTarget.dataset
      
      console.log('日期类型选择器 - 选择类型:', { value, enabled })

      // 如果是禁用状态，不允许选择
      if (!enabled) {
        wx.showToast({
          title: '该类型暂未开放',
          icon: 'none',
          duration: 1500
        })
        return
      }

      // 更新选中状态
      this.setData({
        selectedValue: value
      })

      // 触发选择事件
      this.triggerEvent('select', {
        value: value
      })

      console.log('日期类型选择器 - 已选择类型:', value)
    },

    /**
     * 关闭选择器
     */
    onClose() {
      console.log('日期类型选择器 - 关闭选择器')
      this.triggerEvent('close')
    },

    /**
     * 阻止事件冒泡
     */
    onStopPropagation() {
      // 阻止事件冒泡，防止点击内容区域时关闭模态框
    },

    /**
     * 确认选择
     */
    onConfirm() {
      console.log('日期类型选择器 - 确认选择:', this.data.selectedValue)
      
      this.triggerEvent('confirm', {
        value: this.data.selectedValue
      })
    },

    /**
     * 取消选择
     */
    onCancel() {
      console.log('日期类型选择器 - 取消选择')
      this.triggerEvent('cancel')
    }
  }
})
