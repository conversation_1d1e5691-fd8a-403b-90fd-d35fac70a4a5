/* 摸鱼特效组件样式 */
.fishing-effect-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 999;
  overflow: hidden;
}

.coin {
  position: absolute;
  top: -60rpx;
  font-size: 48rpx;
  user-select: none;
  pointer-events: none;
  z-index: 999;

  /* 添加更丰富的视觉效果 */
  filter: drop-shadow(0 4rpx 8rpx rgba(255, 215, 0, 0.4))
          drop-shadow(0 0 20rpx rgba(255, 215, 0, 0.2));

  /* 初始状态 */
  opacity: 0;
  transform: translateY(0) rotate(0deg);

  /* 添加轻微的闪烁效果 */
  animation: coinGlow 2s ease-in-out infinite alternate;
}

/* 下落动画 */
.coin.falling {
  animation-name: coinFall;
  animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53); /* 更符合重力的缓动函数 */
  animation-fill-mode: forwards;
  opacity: 1;
}

@keyframes coinFall {
  0% {
    opacity: 1;
    transform: translateY(-60rpx) rotate(0deg);
  }
  
  10% {
    opacity: 1;
  }
  
  90% {
    opacity: 1;
  }
  
  100% {
    opacity: 0;
    transform: translateY(calc(100vh + 60rpx)) rotate(360deg);
  }
}

/* 不同的旋转变化 */
.coin.spin-slow {
  animation-name: coinFallSpinSlow;
}

.coin.spin-fast {
  animation-name: coinFallSpinFast;
}

@keyframes coinFallSpinSlow {
  0% {
    opacity: 1;
    transform: translateY(-60rpx) rotate(0deg);
  }
  
  10% {
    opacity: 1;
  }
  
  90% {
    opacity: 1;
  }
  
  100% {
    opacity: 0;
    transform: translateY(calc(100vh + 60rpx)) rotate(180deg);
  }
}

@keyframes coinFallSpinFast {
  0% {
    opacity: 1;
    transform: translateY(-60rpx) rotate(0deg);
  }
  
  10% {
    opacity: 1;
  }
  
  90% {
    opacity: 1;
  }
  
  100% {
    opacity: 0;
    transform: translateY(calc(100vh + 60rpx)) rotate(720deg);
  }
}

/* 轻微的左右摆动效果 */
.coin.sway {
  animation-name: coinFallSway;
}

@keyframes coinFallSway {
  0% {
    opacity: 1;
    transform: translateY(-60rpx) translateX(0) rotate(0deg);
  }

  10% {
    opacity: 1;
  }

  25% {
    transform: translateY(25vh) translateX(20rpx) rotate(90deg);
  }

  50% {
    transform: translateY(50vh) translateX(-10rpx) rotate(180deg);
  }

  75% {
    transform: translateY(75vh) translateX(15rpx) rotate(270deg);
  }

  90% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    transform: translateY(calc(100vh + 60rpx)) translateX(0) rotate(360deg);
  }
}

/* 金币闪烁效果 */
@keyframes coinGlow {
  0% {
    filter: drop-shadow(0 4rpx 8rpx rgba(255, 215, 0, 0.4))
            drop-shadow(0 0 20rpx rgba(255, 215, 0, 0.2));
  }

  100% {
    filter: drop-shadow(0 4rpx 8rpx rgba(255, 215, 0, 0.6))
            drop-shadow(0 0 30rpx rgba(255, 215, 0, 0.4));
  }
}

/* 渐出效果 */
.coin.fade-out {
  animation: fadeOut 1s ease-out forwards !important;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  100% {
    opacity: 0;
    transform: scale(0.5);
  }
}
