# 摸鱼特效组件 (fishing-effect)

一个有趣的摸鱼特效组件，当用户开启摸鱼模式时显示金币雨滴效果。

## 功能特性

- 🪙 **金币雨滴效果**: 金币从屏幕上方落到下方，符合物理效果
- 🎨 **多样化动画**: 包含直落、旋转、摆动等多种下落动画
- ✨ **视觉效果**: 金币带有发光效果和阴影
- 🎯 **性能优化**: 限制同时存在的金币数量，避免性能问题
- 🔧 **可配置参数**: 通过常量轻松调整效果强度
- 📱 **响应式设计**: 适配不同屏幕尺寸

## 使用方法

### 1. 在页面配置中注册组件

```json
{
  "usingComponents": {
    "fishing-effect": "../../components/fishing-effect/index"
  }
}
```

### 2. 在模板中使用组件

```xml
<!-- 摸鱼特效组件 -->
<fishing-effect is-fishing="{{isFishing}}"></fishing-effect>
```

### 3. 在页面逻辑中控制状态

```javascript
Page({
  data: {
    isFishing: false
  },
  
  // 开始摸鱼
  startFishing() {
    this.setData({
      isFishing: true
    })
  },
  
  // 停止摸鱼
  stopFishing() {
    this.setData({
      isFishing: false
    })
  }
})
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| is-fishing | Boolean | false | 是否处于摸鱼状态 |

## 可配置参数

在组件的 `constants` 对象中可以调整以下参数：

```javascript
constants: {
  // 金币生成间隔（毫秒）- 控制金币雨的密度
  COIN_GENERATION_INTERVAL: 1200,
  
  // 金币下落时间范围（毫秒）- 控制下落速度
  COIN_FALL_DURATION_MIN: 2500,
  COIN_FALL_DURATION_MAX: 4000,
  
  // 同时存在的最大金币数量 - 控制性能
  MAX_COINS: 12,
  
  // 金币生成延迟范围（毫秒）- 让金币不会同时出现
  COIN_DELAY_MIN: 0,
  COIN_DELAY_MAX: 800,
  
  // 可用的金币emoji - 随机选择增加趣味性
  COIN_EMOJIS: ['🪙', '💰', '💎', '⭐', '✨', '🌟'],
  
  // 动画类型 - 不同的下落效果
  ANIMATION_TYPES: ['falling', 'spin-slow', 'spin-fast', 'sway'],
  
  // 金币大小变化范围（用于CSS scale）
  COIN_SCALE_MIN: 0.8,
  COIN_SCALE_MAX: 1.2
}
```

## 动画类型说明

- **falling**: 基础直线下落，带重力加速效果
- **spin-slow**: 慢速旋转下落
- **spin-fast**: 快速旋转下落  
- **sway**: 左右摆动下落

## 性能考虑

- 组件会自动限制同时存在的金币数量
- 使用 CSS transform 而非改变位置属性来优化性能
- 动画结束后会自动清理 DOM 元素
- 摸鱼结束时会给现有金币添加渐出效果

## 测试

可以访问 `pages/component-test/index` 页面来测试组件效果。

## 注意事项

1. 组件使用固定定位覆盖整个屏幕，z-index 为 999
2. 金币不会阻挡用户交互（pointer-events: none）
3. 组件会在摸鱼状态变化时自动启动/停止动画
4. 建议在实际使用中根据需要调整常量参数
