/**
 * 摸鱼特效组件
 * 当用户开启摸鱼模式时显示金币雨滴效果
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否处于摸鱼状态
    isFishing: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 是否激活特效
    isActive: false,
    
    // 金币数组
    coins: [],
    
    // 金币emoji
    coinEmoji: '🪙',
    
    // 下一个金币的ID
    nextCoinId: 1
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('[FishingEffect] 组件初始化')

      // 初始化常量配置
      this.initConstants()

      // 获取屏幕信息
      this.getScreenInfo()

      // 随机选择金币emoji
      this.selectRandomCoinEmoji()
    },

    detached() {
      console.log('[FishingEffect] 组件销毁')
      this.cleanup()
    }
  },

  /**
   * 属性监听器
   */
  observers: {
    'isFishing': function(isFishing) {
      console.log('[FishingEffect] 摸鱼状态变化:', isFishing)
      
      if (isFishing) {
        this.startEffect()
      } else {
        this.stopEffect()
      }
    }
  },



  /**
   * 组件方法
   */
  methods: {
    /**
     * 初始化常量配置
     */
    initConstants() {
      this.constants = {
        // 金币生成间隔（毫秒）- 控制金币雨的密度
        COIN_GENERATION_INTERVAL: 1200,

        // 金币下落时间范围（毫秒）- 控制下落速度
        COIN_FALL_DURATION_MIN: 2500,
        COIN_FALL_DURATION_MAX: 4000,

        // 同时存在的最大金币数量 - 控制性能
        MAX_COINS: 12,

        // 金币生成延迟范围（毫秒）- 让金币不会同时出现
        COIN_DELAY_MIN: 0,
        COIN_DELAY_MAX: 800,

        // 可用的金币emoji - 随机选择增加趣味性
        COIN_EMOJIS: ['🪙', '💰', '💎', '⭐', '✨', '🌟'],

        // 动画类型 - 不同的下落效果
        ANIMATION_TYPES: ['falling', 'spin-slow', 'spin-fast', 'sway'],

        // 金币大小变化范围（用于CSS scale）
        COIN_SCALE_MIN: 0.8,
        COIN_SCALE_MAX: 1.2
      }
    },
    /**
     * 获取屏幕信息
     */
    getScreenInfo() {
      try {
        const windowInfo = wx.getWindowInfo()
        this.screenWidth = windowInfo.windowWidth
        this.screenHeight = windowInfo.windowHeight

        console.log('[FishingEffect] 屏幕尺寸:', {
          width: this.screenWidth,
          height: this.screenHeight
        })
      } catch (error) {
        console.error('[FishingEffect] 获取屏幕信息失败:', error)
        // 使用默认值
        this.screenWidth = 375
        this.screenHeight = 667
      }
    },

    /**
     * 随机选择金币emoji
     */
    selectRandomCoinEmoji() {
      const emojis = this.constants.COIN_EMOJIS
      const randomIndex = Math.floor(Math.random() * emojis.length)
      this.setData({
        coinEmoji: emojis[randomIndex]
      })
    },

    /**
     * 开始特效
     */
    startEffect() {
      console.log('[FishingEffect] 开始摸鱼特效')
      
      this.setData({
        isActive: true
      })
      
      // 开始生成金币
      this.startCoinGeneration()
    },

    /**
     * 停止特效
     */
    stopEffect() {
      console.log('[FishingEffect] 停止摸鱼特效')

      // 停止生成新金币
      this.stopCoinGeneration()

      // 给现有金币添加渐出效果
      this.fadeOutExistingCoins()

      // 等待现有金币落完后隐藏容器
      setTimeout(() => {
        this.setData({
          isActive: false,
          coins: []
        })
      }, this.constants.COIN_FALL_DURATION_MAX + 1000)
    },

    /**
     * 开始生成金币
     */
    startCoinGeneration() {
      if (this.coinGenerationTimer) {
        clearInterval(this.coinGenerationTimer)
      }
      
      this.coinGenerationTimer = setInterval(() => {
        if (this.data.isFishing && this.data.coins.length < this.constants.MAX_COINS) {
          this.generateCoin()
        }
      }, this.constants.COIN_GENERATION_INTERVAL)
    },

    /**
     * 停止生成金币
     */
    stopCoinGeneration() {
      if (this.coinGenerationTimer) {
        clearInterval(this.coinGenerationTimer)
        this.coinGenerationTimer = null
      }
    },

    /**
     * 生成一个金币
     */
    generateCoin() {
      const coin = {
        id: this.data.nextCoinId,
        x: this.getRandomX(),
        duration: this.getRandomDuration(),
        delay: this.getRandomDelay(),
        animationClass: this.getRandomAnimationType(),
        scale: this.getRandomScale(),
        emoji: this.getRandomCoinEmoji()
      }

      const newCoins = [...this.data.coins, coin]

      this.setData({
        coins: newCoins,
        nextCoinId: this.data.nextCoinId + 1
      })

      console.log('[FishingEffect] 生成金币:', coin)
    },

    /**
     * 获取随机X位置
     */
    getRandomX() {
      const margin = 30 // 边距
      return Math.random() * (this.screenWidth - margin * 2) + margin
    },

    /**
     * 获取随机下落时间
     */
    getRandomDuration() {
      const min = this.constants.COIN_FALL_DURATION_MIN
      const max = this.constants.COIN_FALL_DURATION_MAX
      return Math.random() * (max - min) + min
    },

    /**
     * 获取随机延迟时间
     */
    getRandomDelay() {
      const min = this.constants.COIN_DELAY_MIN
      const max = this.constants.COIN_DELAY_MAX
      return Math.random() * (max - min) + min
    },

    /**
     * 获取随机动画类型
     */
    getRandomAnimationType() {
      const types = this.constants.ANIMATION_TYPES
      const randomIndex = Math.floor(Math.random() * types.length)
      return types[randomIndex]
    },

    /**
     * 获取随机大小
     */
    getRandomScale() {
      const min = this.constants.COIN_SCALE_MIN
      const max = this.constants.COIN_SCALE_MAX
      return Math.random() * (max - min) + min
    },

    /**
     * 获取随机金币emoji
     */
    getRandomCoinEmoji() {
      const emojis = this.constants.COIN_EMOJIS
      const randomIndex = Math.floor(Math.random() * emojis.length)
      return emojis[randomIndex]
    },

    /**
     * 让现有金币渐出
     */
    fadeOutExistingCoins() {
      const coins = this.data.coins
      if (coins.length === 0) return

      // 给所有现有金币添加渐出类
      const updatedCoins = coins.map(coin => ({
        ...coin,
        animationClass: coin.animationClass + ' fade-out'
      }))

      this.setData({
        coins: updatedCoins
      })
    },

    /**
     * 金币动画结束事件
     */
    onCoinAnimationEnd(e) {
      const coinId = parseInt(e.currentTarget.dataset.id)
      console.log('[FishingEffect] 金币动画结束:', coinId)
      
      // 从数组中移除这个金币
      const newCoins = this.data.coins.filter(coin => coin.id !== coinId)
      this.setData({
        coins: newCoins
      })
    },

    /**
     * 清理资源
     */
    cleanup() {
      this.stopCoinGeneration()
      this.setData({
        coins: [],
        isActive: false
      })
    }
  }
})
