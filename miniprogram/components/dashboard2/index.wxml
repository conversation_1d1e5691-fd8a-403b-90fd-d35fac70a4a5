<!-- 现代仪表盘组件 -->
<view class="dashboard2-container">

  <!-- Dashboard2 导航栏 -->
  <view class="dashboard2-navbar">
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    <view class="navbar-content">
      <!-- 左侧按钮组 -->
      <view class="navbar-left">
        <view class="navbar-button" bindtap="onShowDashboardSwitcher">
          <text class="button-icon">🔄</text>
        </view>
        <view class="navbar-button" bindtap="onShowSettings">
          <text class="button-icon">⚙️</text>
        </view>
      </view>

      <!-- 中间标题 -->
      <view class="navbar-center">
        <text class="navbar-title">时间跟踪器</text>
      </view>

      <!-- 右侧留空，避免被微信菜单覆盖 -->
      <view class="navbar-right">
      </view>
    </view>
  </view>
  <!-- 没有工作履历时的引导 -->
  <view wx:if="{{!hasWorkHistory}}" class="no-work-guide">
    <view class="guide-content">
      <view class="guide-icon">💼</view>
      <view class="guide-title">欢迎使用现代仪表盘</view>
      <view class="guide-text">
        <text>请先添加您的工作履历以使用完整功能</text>
      </view>
      <button class="guide-btn" bindtap="goToWorkHistory">
        <text class="btn-icon">✨</text>
        <text>添加工作履历</text>
      </button>
    </view>
  </view>

  <!-- 有工作履历时显示的内容 -->
  <view wx:else class="main-content">
    <!-- 当前工作显示 -->
    <view class="current-work-section" wx:if="{{showCurrentWork}}">
      <view class="work-header">
        <view class="work-icon">💼</view>
        <view class="work-title">当前工作</view>
      </view>
      <view class="work-content">
        <view class="work-company" bindtap="toggleCompanyMask">
          <text class="work-label">公司</text>
          <text class="work-value">{{companyMasked ? '***' : currentCompany}}</text>
          <text class="mask-toggle">{{companyMasked ? '👁️' : '🙈'}}</text>
        </view>
        <view class="work-position" bindtap="togglePositionMask">
          <text class="work-label">职位</text>
          <text class="work-value">{{positionMasked ? '***' : currentPosition}}</text>
          <text class="mask-toggle">{{positionMasked ? '👁️' : '🙈'}}</text>
        </view>
      </view>
    </view>

    <!-- 顶部统计卡片 -->
    <view class="top-stats">
      <view class="stat-card">
        <view class="stat-icon">🐟</view>
        <view class="stat-info">
          <text class="stat-value">{{todayFishTime}}</text>
          <text class="stat-label">今日摸鱼</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon">⚔️</view>
        <view class="stat-info">
          <text class="stat-value">{{todayWorkTime}}</text>
          <text class="stat-label">今日服役</text>
        </view>
      </view>
      
      <view class="stat-card income-card">
        <view class="stat-icon">💰</view>
        <view class="stat-info">
          <text class="stat-value">{{currencySymbol}}{{todayNetIncome}}</text>
          <text class="stat-label">今日净收入</text>
          <view class="income-breakdown">
            <view class="breakdown-item">
              <text class="breakdown-label">基础</text>
              <text class="breakdown-value work">{{currencySymbol}}{{todayIncome}}</text>
            </view>
            <view class="breakdown-item" wx:if="{{todayExtraIncomeValue > 0}}">
              <text class="breakdown-label">额外</text>
              <text class="breakdown-value extra">+{{currencySymbol}}{{todayExtraIncome}}</text>
            </view>
            <view class="breakdown-item" wx:if="{{todayDeductionsValue > 0}}">
              <text class="breakdown-label">扣款</text>
              <text class="breakdown-value deduction">-{{currencySymbol}}{{todayDeductions}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 中央圆形进度条 -->
    <view class="center-progress">
      <circular-progress
        progress="{{countdownProgress}}"
        size="{{400}}"
        stroke-width="{{12}}"
        stroke-color="#ff6b6b"
        background-color="#f0f0f0"
        animation-duration="{{500}}">

        <view class="countdown-display">
          <view class="countdown-time">
            <text class="time-part">{{countdown.hours}}</text>
            <text class="time-separator">:</text>
            <text class="time-part">{{countdown.minutes}}</text>
            <text class="time-separator">:</text>
            <text class="time-part">{{countdown.seconds}}</text>
          </view>
          <view class="countdown-text">{{countdown.text}}</view>
          <view class="progress-text">进度 {{Math.round(countdownProgress)}}%</view>
        </view>
      </circular-progress>
    </view>

    <!-- 底部统计网格 -->
    <view class="bottom-stats">
      <!-- 第一行 -->
      <view class="stats-row">
        <view class="stat-item">
          <view class="stat-icon">💵</view>
          <view class="stat-content">
            <text class="stat-value">{{currencySymbol}}{{monthSalary}}</text>
            <text class="stat-label">本月时薪</text>
          </view>
          <view class="stat-mascot">🐱</view>
        </view>
        
        <view class="stat-item">
          <view class="stat-icon">📈</view>
          <view class="stat-content">
            <text class="stat-value">{{currencySymbol}}{{yearIncome}}</text>
            <text class="stat-label">今年收入</text>
          </view>
          <view class="stat-mascot">🐰</view>
        </view>
      </view>

      <!-- 第二行 -->
      <view class="stats-row">
        <view class="stat-item">
          <view class="stat-icon">⏰</view>
          <view class="stat-content">
            <text class="stat-value">{{workDuration}}</text>
            <text class="stat-label">工作时长</text>
          </view>
          <view class="stat-mascot">🐻</view>
        </view>
        
        <view class="stat-item">
          <view class="stat-icon">🐟</view>
          <view class="stat-content">
            <text class="stat-value">{{fishingCount}}</text>
            <text class="stat-label">摸鱼人数</text>
          </view>
          <view class="stat-mascot">🐸</view>
        </view>
      </view>

      <!-- 第三行 -->
      <view class="stats-row">
        <view class="stat-item">
          <view class="stat-icon">📅</view>
          <view class="stat-content">
            <text class="stat-value">还有{{nonWorkDays}}天</text>
            <text class="stat-label">距离非工作日</text>
          </view>
          <view class="stat-mascot">🐼</view>
        </view>
        
        <view class="stat-item">
          <view class="stat-icon">🏖️</view>
          <view class="stat-content">
            <text class="stat-value">离国庆节{{nextHoliday}}天</text>
            <text class="stat-label">最近的假期</text>
          </view>
          <view class="stat-mascot">🐨</view>
        </view>
      </view>

      <!-- 第四行 -->
      <view class="stats-row">
        <view class="stat-item">
          <view class="stat-icon">💳</view>
          <view class="stat-content">
            <text class="stat-value" wx:if="{{payDayName === '未设置'}}">未设置</text>
            <text class="stat-value" wx:elif="{{payDay == '0' || payDay == 0}}">今天</text>
            <text class="stat-value" wx:else>{{payDay}}天</text>
            <text class="stat-label" wx:if="{{payDayName === '未设置'}}">发薪日期</text>
            <text class="stat-label" wx:elif="{{payDay == '0' || payDay == 0}}">{{payDayName}}</text>
            <text class="stat-label" wx:else>距离{{payDayName}}</text>
          </view>
          <view class="stat-mascot">🦊</view>
        </view>
        
        <view class="stat-item">
          <view class="stat-icon">👔</view>
          <view class="stat-content">
            <text class="stat-value">{{workDays}}天</text>
            <text class="stat-label">你已入职</text>
          </view>
          <view class="stat-mascot">🐯</view>
        </view>
      </view>
    </view>

    <!-- 摸鱼控制 -->
    <view class="fishing-control-section">
      <fishing-control
        type="default"
        bindfishingstart="onFishingStart"
        bindfishingend="onFishingEnd">
      </fishing-control>
    </view>
  </view>

  <!-- Dashboard2 设置模态框 -->
  <view wx:if="{{showSettings}}" class="settings-overlay" bindtap="onHideSettings">
    <view class="settings-content" catchtap="onModalContentTap">
      <view class="settings-header">
        <text class="settings-title">Dashboard2 设置</text>
        <view class="settings-close" bindtap="onHideSettings">✕</view>
      </view>

      <view class="settings-body">
        <view class="setting-item">
          <text class="setting-label">显示当前工作</text>
          <switch
            checked="{{dashboardConfig.showCurrentWork}}"
            bindchange="onConfigChange"
            data-key="showCurrentWork" />
        </view>

        <view class="setting-item">
          <text class="setting-label">显示倒计时</text>
          <switch
            checked="{{dashboardConfig.showCountdown}}"
            bindchange="onConfigChange"
            data-key="showCountdown" />
        </view>

        <view class="setting-item">
          <text class="setting-label">显示所有统计</text>
          <switch
            checked="{{dashboardConfig.showAllStats}}"
            bindchange="onConfigChange"
            data-key="showAllStats" />
        </view>

        <view class="setting-item">
          <text class="setting-label">进度条大小 ({{dashboardConfig.circularProgressSize}}rpx)</text>
          <slider
            min="300"
            max="500"
            value="{{dashboardConfig.circularProgressSize}}"
            bindchange="onConfigChange"
            data-key="circularProgressSize"
            show-value />
        </view>

        <view class="setting-item">
          <text class="setting-label">收入小数位数</text>
          <picker
            mode="selector"
            range="{{decimalOptions}}"
            range-key="label"
            value="{{selectedDecimalIndex}}"
            bindchange="onDecimalChange">
            <view class="picker-value">{{decimalOptions[selectedDecimalIndex].label}}</view>
          </picker>
        </view>
      </view>

      <view class="settings-actions">
        <button class="action-btn cancel" bindtap="onHideSettings">取消</button>
        <button class="action-btn confirm" bindtap="onSaveSettings">保存</button>
      </view>
    </view>
  </view>
</view>
