<!-- 时间可视化图表 -->
<view class="time-chart" style="height: {{chartHeight}}rpx;">
  
  <!-- 图表容器 -->
  <view class="chart-container" bind:tap="onChartTap">
    <!-- 时间刻度 -->
    <view class="time-scale">
      <view 
        class="scale-item" 
        wx:for="{{timeScale}}" 
        wx:key="hour"
        style="left: {{item.position}}%;">
        <view class="scale-line"></view>
        <view class="scale-text">{{item.text}}</view>
      </view>
    </view>
    
    <!-- 时间段可视化 -->
    <view class="segments-container">
      <!-- 工作时间段 -->
      <view
        class="segment-bar segment-{{item.type}} {{item.isCurrent ? 'segment-current' : ''}}"
        wx:for="{{visualSegments}}"
        wx:key="id"
        style="left: {{item.left}}%; width: {{item.width}}%; background-color: {{item.color}};">

        <!-- 时间段信息 -->
        <view class="segment-info">
          <view class="segment-type">{{item.typeText}}</view>
          <view class="segment-time">{{item.duration}}</view>
        </view>
      </view>

      <!-- 摸鱼时间段（覆盖在工作时间段之上） -->
      <view
        class="fishing-bar {{fish.isCurrent ? 'current-fishing' : ''}}"
        wx:for="{{visualFishes}}"
        wx:key="id"
        wx:for-item="fish"
        style="left: {{fish.left}}%; width: {{fish.width}}%; background-color: {{fish.color}}; opacity: {{fish.opacity}};">

        <!-- 摸鱼信息 -->
        <view class="fishing-info">
          <view class="fishing-icon">{{fish.isCurrent ? '🎣' : '🐟'}}</view>
          <view class="fishing-time">{{fish.startTime}}-{{fish.endTime}}</view>
          <view wx:if="{{fish.remark}}" class="fishing-remark">{{fish.remark}}</view>
          <view wx:if="{{fish.isCurrent}}" class="current-indicator">进行中</view>
        </view>
      </view>
    </view>
    
    <!-- 当前时间线 -->
    <view 
      class="current-time-line" 
      wx:if="{{currentTimePosition >= 0}}"
      style="left: {{currentTimePosition}}%;">
      <view class="time-line"></view>
      <view class="time-dot"></view>
    </view>
  </view>
  
  <!-- 图表统计 -->
  <view class="chart-stats" wx:if="{{totalDuration !== '0小时'}}">
    <view class="stat-item">
      <text class="stat-label">总时长:</text>
      <text class="stat-value">{{totalDuration}}</text>
    </view>
    <view class="stat-item">
      <text class="stat-label">工作比例:</text>
      <text class="stat-value">{{workRatio}}%</text>
    </view>
    <view class="stat-item" wx:if="{{fishingRatio > 0}}">
      <text class="stat-label">摸鱼比例:</text>
      <text class="stat-value fishing-stat">{{fishingRatio}}%</text>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-chart" wx:if="{{visualSegments.length === 0}}">
    <view class="empty-icon">📊</view>
    <view class="empty-text">暂无时间安排</view>
  </view>
</view> 