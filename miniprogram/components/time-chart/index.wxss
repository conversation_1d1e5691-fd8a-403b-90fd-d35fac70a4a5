/* 时间可视化图表样式 */
.time-chart {
  position: relative;
  width: auto;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 24rpx 0;
  border: 2rpx solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}


/* 图表容器 */
.chart-container {
  position: relative;
  width: 100%;
}

/* 时间刻度 */
.time-scale {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  z-index: 1;
}

.scale-item {
  position: absolute;
  transform: translateX(-50%);
}

.scale-line {
  width: 2rpx;
  height: 16rpx;
  background-color: rgba(100, 116, 139, 0.3);
  margin: 0 auto;
}

.scale-text {
  font-size: 20rpx;
  color: #64748b;
  text-align: center;
  margin-top: 4rpx;
  white-space: nowrap;
}

/* 时间段容器 */
.segments-container {
  position: absolute;
  top: 50rpx;
  left: 0;
  right: 0;
  height: 80rpx;
}

/* 时间段条 */
.segment-bar {
  position: absolute;
  height: 64rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

/* 当前时间段高亮 */
.segment-current {
  animation: pulse 2s infinite;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.3);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.85; }
}

/* 时间段信息 */
.segment-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  font-size: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  pointer-events: none;
}

/* 摸鱼时间段 */
.fishing-bar {
  position: absolute;
  height: 64rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(251, 191, 36, 0.3);
  border: 2rpx solid rgba(251, 191, 36, 0.6);
  overflow: hidden;
  z-index: 10; /* 确保摸鱼时间段显示在工作时间段之上 */
  background: repeating-linear-gradient(
    45deg,
    rgba(251, 191, 36, 0.8),
    rgba(251, 191, 36, 0.8) 10rpx,
    rgba(251, 191, 36, 0.6) 10rpx,
    rgba(251, 191, 36, 0.6) 20rpx
  ); /* 斜纹图案表示摸鱼 */
}

.fishing-bar:hover {
  opacity: 1 !important;
  transform: translateY(-2rpx);
}

/* 摸鱼信息 */
.fishing-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #92400e;
  font-size: 18rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  pointer-events: none;
}

.fishing-icon {
  font-size: 24rpx;
  margin-bottom: 2rpx;
}

.fishing-time {
  font-size: 16rpx;
  margin-bottom: 2rpx;
}

.fishing-remark {
  font-size: 14rpx;
  opacity: 0.8;
  max-width: 100rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 当前摸鱼样式 */
.current-fishing {
  animation: fishing-pulse 2s infinite;
  border: 1rpx solid #f59e0b;
  box-shadow: 0 0 8rpx rgba(245, 158, 11, 0.3);
}

.current-indicator {
  font-size: 12rpx;
  color: #f59e0b;
  font-weight: 600;
  margin-top: 2rpx;
  animation: blink 1.5s infinite;
}

@keyframes fishing-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.5;
  }
}

.segment-type {
  font-weight: 600;
  font-size: 22rpx;
}

.segment-time {
  font-size: 18rpx;
  opacity: 0.9;
  margin-top: 2rpx;
}

/* 当前时间线 */
.current-time-line {
  position: absolute;
  top: 40rpx;
  height: 90rpx;
  z-index: 10;
  transition: all 0.3s ease;
  animation: fadeIn 0.3s forwards;
  pointer-events: none;
}

.time-line {
  width: 4rpx;
  height: 100%;
  background: linear-gradient(to bottom, #ef4444, #dc2626);
  border-radius: 2rpx;
  box-shadow: 0 0 8rpx rgba(239, 68, 68, 0.5);
}

.time-dot {
  position: absolute;
  top: -20rpx;
  left: -10rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ef4444;
  border: 4rpx solid white;
  border-radius: 50%;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

/* 图表统计 */
.chart-stats {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 48rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #64748b;
}

.stat-value {
  font-size: 24rpx;
  color: #1e293b;
  font-weight: 600;
}

.stat-value.fishing-stat {
  color: #f59e0b;
}

/* 空状态 */
.empty-chart {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #94a3b8;
}

.empty-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 24rpx;
  color: #64748b;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .time-chart {
    padding: 24rpx;
  }
  
  .segment-info {
    font-size: 18rpx;
  }
  
  .segment-type {
    font-size: 20rpx;
  }
  
  .segment-time {
    font-size: 16rpx;
  }
} 