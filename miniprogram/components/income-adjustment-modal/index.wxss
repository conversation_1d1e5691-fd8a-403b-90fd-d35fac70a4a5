/* 收入调整模态框样式 */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  padding: 20rpx; /* 添加内边距，确保模态框不贴边 */
  box-sizing: border-box;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-container {
  background-color: #ffffff;
  border-radius: 16rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: min(75vh, 900rpx); /* 减少最大高度，确保按钮可见 */
  margin: 20rpx 0; /* 添加上下边距 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal-overlay.show .modal-container {
  transform: scale(1);
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.close-btn {
  font-size: 40rpx;
  color: #999999;
  padding: 8rpx;
  line-height: 1;
}

/* 模态框内容 */
.modal-content {
  flex: 1;
  padding: 32rpx;
  overflow-y: auto;
  min-height: 0; /* 允许内容区域收缩 */
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.optional-text {
  font-size: 24rpx;
  color: #999999;
  font-weight: 400;
}

.form-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.picker-text {
  font-size: 30rpx;
  color: #333333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999999;
}

.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  font-size: 30rpx;
  color: #333333;
  box-sizing: border-box;
}

.form-input.error {
  border-color: #dc3545;
  background-color: #fff5f5;
}

.error-text {
  display: block;
  font-size: 24rpx;
  color: #dc3545;
  margin-top: 8rpx;
}

/* 预设描述标签 */
.preset-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.preset-tag {
  padding: 12rpx 24rpx;
  background-color: #e3f2fd;
  color: #1976d2;
  border-radius: 20rpx;
  font-size: 26rpx;
  border: 1rpx solid #bbdefb;
  transition: all 0.2s ease;
}

.preset-tag:active {
  background-color: #1976d2;
  color: #ffffff;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  padding: 32rpx;
  gap: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
  flex-shrink: 0; /* 防止按钮区域被压缩 */
}

.btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  text-align: center;
  border: none;
  transition: all 0.2s ease;
}

.btn-cancel {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1rpx solid #dee2e6;
}

.btn-cancel:active {
  background-color: #e9ecef;
}

.btn-confirm {
  background-color: #007bff;
  color: #ffffff;
}

.btn-confirm:active {
  background-color: #0056b3;
}

.btn-confirm.loading {
  background-color: #6c757d;
  opacity: 0.7;
}

.btn-confirm:disabled {
  background-color: #6c757d;
  opacity: 0.5;
}

/* 小屏幕适配 */
@media (max-height: 600px) {
  .modal-container {
    max-height: 90vh;
    margin: 10rpx 0;
  }

  .modal-content {
    padding: 24rpx;
  }

  .modal-footer {
    padding: 24rpx;
  }

  .form-group {
    margin-bottom: 24rpx;
  }
}

/* 极小屏幕适配 */
@media (max-height: 500px) {
  .modal-container {
    max-height: 95vh;
    margin: 5rpx 0;
  }

  .modal-content {
    padding: 16rpx;
  }

  .modal-footer {
    padding: 16rpx;
  }

  .form-group {
    margin-bottom: 16rpx;
  }

  .btn {
    padding: 16rpx;
    font-size: 28rpx;
  }
}
