/**
 * 响应格式化工具函数
 */

/**
 * 成功响应
 * @param {*} data - 响应数据
 * @param {string} message - 响应消息
 * @returns {Object} 格式化的成功响应
 */
function success(data = null, message = '操作成功') {
  return {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 失败响应
 * @param {string} errMsg - 错误消息
 * @param {string} errCode - 错误代码
 * @param {*} data - 额外数据
 * @returns {Object} 格式化的失败响应
 */
function error(errMsg = '操作失败', errCode = 'UNKNOWN_ERROR', data = null) {
  return {
    success: false,
    errMsg,
    errCode,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 权限不足响应
 * @param {string} featureName - 功能名称
 * @param {string} requiredMembership - 所需会员等级
 * @returns {Object} 权限不足响应
 */
function permissionDenied(featureName = '此功能', requiredMembership = 'pro') {
  return {
    success: false,
    errMsg: `${featureName}仅限${requiredMembership === 'pro' ? 'Pro会员' : '高级会员'}使用`,
    errCode: 'PERMISSION_DENIED',
    needUpgrade: true,
    membershipRequired: requiredMembership,
    timestamp: new Date().toISOString()
  }
}

/**
 * 参数错误响应
 * @param {string} paramName - 参数名称
 * @param {string} reason - 错误原因
 * @returns {Object} 参数错误响应
 */
function invalidParam(paramName, reason = '参数无效') {
  return {
    success: false,
    errMsg: `参数错误: ${paramName} - ${reason}`,
    errCode: 'INVALID_PARAM',
    param: paramName,
    timestamp: new Date().toISOString()
  }
}

/**
 * 资源不存在响应
 * @param {string} resourceName - 资源名称
 * @returns {Object} 资源不存在响应
 */
function notFound(resourceName = '资源') {
  return {
    success: false,
    errMsg: `${resourceName}不存在`,
    errCode: 'NOT_FOUND',
    timestamp: new Date().toISOString()
  }
}

/**
 * 服务器内部错误响应
 * @param {string} message - 错误消息
 * @returns {Object} 服务器错误响应
 */
function serverError(message = '服务器内部错误') {
  return {
    success: false,
    errMsg: message,
    errCode: 'SERVER_ERROR',
    timestamp: new Date().toISOString()
  }
}

/**
 * 频率限制响应
 * @param {string} message - 限制消息
 * @param {Object} limitInfo - 限制信息
 * @returns {Object} 频率限制响应
 */
function rateLimited(message = 'API调用频率超限', limitInfo = {}) {
  return {
    success: false,
    errMsg: message,
    errCode: 'RATE_LIMITED',
    limitInfo,
    timestamp: new Date().toISOString()
  }
}

/**
 * 分页响应
 * @param {Array} data - 数据列表
 * @param {number} total - 总数
 * @param {number} page - 当前页
 * @param {number} pageSize - 页大小
 * @param {string} message - 响应消息
 * @returns {Object} 分页响应
 */
function paginated(data = [], total = 0, page = 1, pageSize = 20, message = '查询成功') {
  return {
    success: true,
    message,
    data,
    pagination: {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
      hasNext: page * pageSize < total,
      hasPrev: page > 1
    },
    timestamp: new Date().toISOString()
  }
}

/**
 * 包装异步函数，统一错误处理
 * @param {Function} fn - 异步函数
 * @returns {Function} 包装后的函数
 */
function wrapAsync(fn) {
  return async (...args) => {
    try {
      const result = await fn(...args)
      
      // 如果结果已经是标准格式，直接返回
      if (result && typeof result === 'object' && 'success' in result) {
        return result
      }
      
      // 否则包装为成功响应
      return success(result)
    } catch (err) {
      console.error('API执行错误:', err)
      
      // 根据错误类型返回不同响应
      if (err.name === 'ValidationError') {
        return invalidParam(err.path, err.message)
      }
      
      if (err.name === 'PermissionError') {
        return permissionDenied(err.feature, err.required)
      }
      
      if (err.name === 'NotFoundError') {
        return notFound(err.resource)
      }
      
      return serverError(err.message || '未知错误')
    }
  }
}

/**
 * 验证必需参数
 * @param {Object} params - 参数对象
 * @param {Array} required - 必需参数列表
 * @throws {Error} 参数验证失败时抛出错误
 */
function validateRequired(params, required) {
  for (const param of required) {
    if (params[param] === undefined || params[param] === null || params[param] === '') {
      const err = new Error(`缺少必需参数: ${param}`)
      err.name = 'ValidationError'
      err.path = param
      throw err
    }
  }
}

/**
 * 验证参数类型
 * @param {*} value - 参数值
 * @param {string} type - 期望类型
 * @param {string} paramName - 参数名称
 * @throws {Error} 类型验证失败时抛出错误
 */
function validateType(value, type, paramName) {
  const actualType = typeof value
  
  if (type === 'array' && !Array.isArray(value)) {
    const err = new Error(`参数类型错误，期望数组`)
    err.name = 'ValidationError'
    err.path = paramName
    throw err
  }
  
  if (type !== 'array' && actualType !== type) {
    const err = new Error(`参数类型错误，期望${type}，实际${actualType}`)
    err.name = 'ValidationError'
    err.path = paramName
    throw err
  }
}

module.exports = {
  success,
  error,
  permissionDenied,
  invalidParam,
  notFound,
  serverError,
  rateLimited,
  paginated,
  wrapAsync,
  validateRequired,
  validateType
}
