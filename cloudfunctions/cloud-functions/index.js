const cloud = require("wx-server-sdk");
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
});

const db = cloud.database();
// 获取openid
const getOpenId = async () => {
  // 获取基础信息
  const wxContext = cloud.getWXContext();
  return {
    openid: wxContext.OPENID,
    appid: wxContext.APPID,
    unionid: wxContext.UNIONID,
  };
};

// 获取小程序二维码
const getMiniProgramCode = async () => {
  // 获取小程序二维码的buffer
  const resp = await cloud.openapi.wxacode.get({
    path: "pages/index/index",
  });
  const { buffer } = resp;
  // 将图片上传云存储空间
  const upload = await cloud.uploadFile({
    cloudPath: "code.png",
    fileContent: buffer,
  });
  return upload.fileID;
};

// 创建集合
const createCollection = async () => {
  try {
    // 创建集合
    await db.createCollection("sales");
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华东",
        city: "上海",
        sales: 11,
      },
    });
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华东",
        city: "南京",
        sales: 11,
      },
    });
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华南",
        city: "广州",
        sales: 22,
      },
    });
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华南",
        city: "深圳",
        sales: 22,
      },
    });
    return {
      success: true,
    };
  } catch (e) {
    // 这里catch到的是该collection已经存在，从业务逻辑上来说是运行成功的，所以catch返回success给前端，避免工具在前端抛出异常
    return {
      success: true,
      data: "create collection success",
    };
  }
};

// 查询数据
const selectRecord = async () => {
  // 返回数据库查询结果
  return await db.collection("sales").get();
};

// 更新数据
const updateRecord = async (event) => {
  try {
    // 遍历修改数据库信息
    for (let i = 0; i < event.data.length; i++) {
      await db
        .collection("sales")
        .where({
          _id: event.data[i]._id,
        })
        .update({
          data: {
            sales: event.data[i].sales,
          },
        });
    }
    return {
      success: true,
      data: event.data,
    };
  } catch (e) {
    return {
      success: false,
      errMsg: e,
    };
  }
};

// 新增数据
const insertRecord = async (event) => {
  try {
    const insertRecord = event.data;
    // 插入数据
    await db.collection("sales").add({
      data: {
        region: insertRecord.region,
        city: insertRecord.city,
        sales: Number(insertRecord.sales),
      },
    });
    return {
      success: true,
      data: event.data,
    };
  } catch (e) {
    return {
      success: false,
      errMsg: e,
    };
  }
};

// 删除数据
const deleteRecord = async (event) => {
  try {
    await db
      .collection("sales")
      .where({
        _id: event.data._id,
      })
      .remove();
    return {
      success: true,
    };
  } catch (e) {
    return {
      success: false,
      errMsg: e,
    };
  }
};

const { getHolidayData, getWeekendData } = require('./api/holiday');
const { getChangelogList, createChangelogCollection } = require('./api/changelog');
const { getUserInfo, updateUserInfo, getVipRecords, getVipRecordsStats, getUserRedemptionCodes, useRedemptionCode } = require('./api/user');
const { checkIn, getCheckInStatus, getCheckInCalendar, getCheckInHistory } = require('./api/check-in');
const { getPointsBalance, getPointsRecords, getPointsStats } = require('./api/points');
const {
  getStoreItems, purchaseItem, getMyRedemptionCodes, redeemCode,
  initializeStoreItems
} = require('./api/store');
const { getCloudDataInfo, downloadUserData, uploadUserData, getHistoryDataList, getHistoryData } = require('./api/user-data');
const { processInvitation, getInvitationStats } = require('./api/invitation');

// const getOpenId = require('./getOpenId/index');
// const getMiniProgramCode = require('./getMiniProgramCode/index');
// const createCollection = require('./createCollection/index');
// const selectRecord = require('./selectRecord/index');
// const updateRecord = require('./updateRecord/index');
// const sumRecord = require('./sumRecord/index');
// const fetchGoodsList = require('./fetchGoodsList/index');
// const genMpQrcode = require('./genMpQrcode/index');

// 云函数入口函数
exports.main = async (event, context) => {
  console.info(`==================== 云函数被调用事件：`, event)

  switch (event.type) {
    case "getOpenId":
      return await getOpenId();
    case "getMiniProgramCode":
      return await getMiniProgramCode();
    case "createCollection":
      return await createCollection();
    case "selectRecord":
      return await selectRecord();
    case "updateRecord":
      return await updateRecord(event);
    case "insertRecord":
      return await insertRecord(event);
    case "deleteRecord":
      return await deleteRecord(event);

    // 节假日
    case "getHolidayData":
      // 获取节假日数据
      return await getHolidayData(event.data);
    case "getWeekendData":
      // 获取周末数据
      return await getWeekendData(event.data);

    // 更新日志
    case "createChangelogCollection":
      // 创建更新日志集合（初始化数据）
      return await createChangelogCollection(event.data);
    case "getChangelogList":
      // 获取更新日志列表
      return await getChangelogList(event.data);

    // 用户系统
    case "getUserInfo":
      // 获取用户信息
      return await getUserInfo(event.data);
    case "updateUserInfo":
      // 更新用户信息
      return await updateUserInfo(event.data);
    case "getVipRecords":
      // 获取VIP记录
      return await getVipRecords(event.data);
    case "getVipRecordsStats":
      // 获取VIP记录统计
      return await getVipRecordsStats(event.data);
    case "getUserRedemptionCodes":
      // 获取用户兑换码
      return await getUserRedemptionCodes(event.data);
    case "useRedemptionCode":
      // 使用兑换码
      return await useRedemptionCode(event.data);

    // 签到相关API
    case "checkIn":
      // 执行签到
      return await checkIn(event.data);
    case "getCheckInStatus":
      // 获取签到状态
      return await getCheckInStatus(event.data);
    case "getCheckInCalendar":
      // 获取签到日历
      return await getCheckInCalendar(event.data);
    case "getCheckInHistory":
      // 获取签到历史
      return await getCheckInHistory(event.data);

    // 积分相关API
    case "getPointsBalance":
      // 获取积分余额
      return await getPointsBalance(event.data);
    case "getPointsRecords":
      // 获取积分记录
      return await getPointsRecords(event.data);
    case "getPointsStats":
      // 获取积分统计
      return await getPointsStats(event.data);

    // 邀请好友相关API
    case "processInvitation":
      // 处理邀请绑定
      return await processInvitation(event.data);
    case "getInvitationStats":
      // 获取邀请统计
      return await getInvitationStats(event.data);

    // 商店相关API
    case "getStoreItems":
      // 获取商店商品
      return await getStoreItems(event.data);

    case "purchaseItem":
      // 购买商品
      return await purchaseItem(event.data);
    case "getMyRedemptionCodes":
      // 获取我的兑换码
      return await getMyRedemptionCodes(event.data);
    case "redeemCode":
      // 使用兑换码
      return await redeemCode(event.data);

    // 系统内部API
    case "initializeStoreItems":
      // 初始化商店商品（系统内部调用）
      return await initializeStoreItems(event.data);
    case "testDatabase":
      // 测试数据库连接
      return await testDatabase(event.data);

    // 数据同步
    case "getCloudDataInfo":
      // 获取云端数据信息
      return await getCloudDataInfo(event.data);
    case "downloadUserData":
      // 下载用户数据
      return await downloadUserData(event.data);
    case "uploadUserData":
      // 上传用户数据
      return await uploadUserData(event.data);
    case "getHistoryDataList":
      // 获取历史数据列表
      return await getHistoryDataList(event.data);
    case "getHistoryData":
      // 获取指定历史数据
      return await getHistoryData(event.data);

    default:
      return {
        success: false,
        errMsg: "未知的操作类型"
      };
  }
};
