/**
 * 更新日志数据库操作
 */

const BaseDB = require('./base')

class ChangelogDB extends BaseDB {
  constructor() {
    super('changelog')
  }

  /**
   * 创建更新日志
   * @param {Object} logData - 日志数据
   * @returns {Promise<Object>} 创建结果
   */
  async createLog(logData) {
    const defaultLogData = {
      version: logData.version,
      title: logData.title,
      content: logData.content,
      type: logData.type || 'update', // update, feature, bugfix
      publishTime: logData.publishTime || new Date(),
      isPublished: logData.isPublished !== undefined ? logData.isPublished : true,
      ...logData
    }

    return await this.create(defaultLogData)
  }

  /**
   * 获取已发布的更新日志列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getPublishedLogs(options = {}) {
    const queryOptions = {
      orderBy: { field: 'publishTime', order: 'desc' },
      limit: options.limit || 20,
      skip: options.skip || 0
    }

    return await this.find({ isPublished: true }, queryOptions)
  }

  /**
   * 获取所有更新日志（包括未发布）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getAllLogs(options = {}) {
    const queryOptions = {
      orderBy: { field: 'publishTime', order: 'desc' },
      limit: options.limit || 50,
      skip: options.skip || 0
    }

    return await this.find({}, queryOptions)
  }

  /**
   * 根据版本号获取日志
   * @param {string} version - 版本号
   * @returns {Promise<Object>} 查询结果
   */
  async getLogByVersion(version) {
    return await this.findOne({ version })
  }

  /**
   * 发布日志
   * @param {string} logId - 日志ID
   * @returns {Promise<Object>} 更新结果
   */
  async publishLog(logId) {
    return await this.updateById(logId, {
      isPublished: true,
      publishTime: new Date()
    })
  }

  /**
   * 取消发布日志
   * @param {string} logId - 日志ID
   * @returns {Promise<Object>} 更新结果
   */
  async unpublishLog(logId) {
    return await this.updateById(logId, {
      isPublished: false
    })
  }

  /**
   * 更新日志内容
   * @param {string} logId - 日志ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateLog(logId, updateData) {
    return await this.updateById(logId, updateData)
  }

  /**
   * 删除日志
   * @param {string} logId - 日志ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteLog(logId) {
    return await this.deleteById(logId)
  }

  /**
   * 获取最新版本号
   * @returns {Promise<Object>} 查询结果
   */
  async getLatestVersion() {
    try {
      const result = await this.find(
        { isPublished: true },
        {
          orderBy: { field: 'publishTime', order: 'desc' },
          limit: 1
        }
      )

      if (!result.success || result.data.length === 0) {
        return {
          success: true,
          data: { version: '1.0.0' }
        }
      }

      return {
        success: true,
        data: { version: result.data[0].version }
      }
    } catch (error) {
      console.error('获取最新版本失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取最新版本失败'
      }
    }
  }

  /**
   * 初始化默认更新日志
   * @returns {Promise<Object>} 初始化结果
   */
  async initDefaultLogs() {
    try {
      // 检查是否已有日志
      const existingResult = await this.count()
      if (!existingResult.success) {
        throw new Error('检查现有日志失败')
      }

      if (existingResult.data.total > 0) {
        return {
          success: true,
          message: '更新日志已存在，跳过初始化'
        }
      }

      // 创建默认日志
      const defaultLogs = [
        {
          version: '1.0.0',
          title: '时间跟踪器正式发布',
          content: '• 基础时间追踪功能\n• 工作履历管理\n• 收入统计\n• 日历视图',
          type: 'release',
          publishTime: new Date('2024-01-01')
        }
      ]

      const results = []
      for (const log of defaultLogs) {
        const result = await this.createLog(log)
        results.push(result)
      }

      return {
        success: true,
        message: '默认更新日志初始化完成',
        data: results
      }
    } catch (error) {
      console.error('初始化默认日志失败:', error)
      return {
        success: false,
        errMsg: error.message || '初始化默认日志失败'
      }
    }
  }
}

module.exports = new ChangelogDB()
