/**
 * 用户数据库操作
 */

const BaseDB = require('./base')
const { isTestUser } = require('../utils/test-user')

class UsersDB extends BaseDB {
  constructor() {
    super('users')
  }

  /**
   * 根据openid查找用户
   * @param {string} openid - 用户openid
   * @returns {Promise<Object>} 查询结果
   */
  async findByOpenid(openid) {
    return await this.findOne({ openid })
  }

  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @returns {Promise<Object>} 创建结果
   */
  async createUser(userData) {
    // 获取下一个用户 ID
    const userNo = await this.getNextUserNo()

    const defaultUserData = {
      openid: userData.openid,
      no: userNo, // 用户编号，从1开始
      avatar: userData.avatar || '',
      nickname: userData.nickname || '神秘用户',
      isTestUser: isTestUser(userData.openid), // 检查是否为测试用户
      vip: {
        status: false,
        expiredAt: null
      },
      apiCallCount: {
        daily: 0,
        monthly: 0,
        lastResetDate: new Date().toISOString().split('T')[0]
      },
      checkInStats: {
        totalDays: 0,
        consecutiveDays: 0,
        lastCheckInDate: null,
        longestStreak: 0
      },
      points: 0, // 当前积分余额
      invitation: {
        invitedBy: null, // 邀请人用户ID
        invitedAt: null, // 被邀请时间
        invitedUsers: [], // 邀请的用户ID列表
        totalInvited: 0, // 总邀请人数
        rewardClaimed: false // 是否已领取邀请奖励
      },
      settings: {},
      ...userData
    }

    return await this.create(defaultUserData)
  }

  /**
   * 获取下一个用户编号
   * @returns {Promise<number>} 用户编号
   */
  async getNextUserNo() {
    try {
      // 获取当前最大的用户编号
      const result = await this.collection
        .orderBy('no', 'desc')
        .limit(1)
        .get()

      if (result.data && result.data.length > 0) {
        return (result.data[0].no || 0) + 1
      } else {
        return 1 // 第一个用户
      }
    } catch (error) {
      console.error('获取用户编号失败:', error)
      // 如果获取失败，使用时间戳作为备用方案
      return Date.now() % 1000000
    }
  }

  /**
   * 更新用户信息
   * @param {string} openid - 用户openid
   * @param {Object} userData - 更新的用户数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateByOpenid(openid, userData) {
    return await this.update({ openid }, userData)
  }

  /**
   * 更新用户API调用计数
   * @param {string} openid - 用户openid
   * @param {Object} apiCallCount - API调用计数
   * @returns {Promise<Object>} 更新结果
   */
  async updateApiCallCount(openid, apiCallCount) {
    return await this.update({ openid }, { apiCallCount })
  }

  /**
   * 检查用户是否为VIP会员
   * @param {string} openid - 用户openid
   * @returns {Promise<boolean>} 是否为VIP会员
   */
  async isVipMember(openid) {
    try {
      const result = await this.findByOpenid(openid)
      if (!result.success || !result.data) {
        return false
      }
      return result.data.vip?.status === true
    } catch (error) {
      console.error('检查VIP会员状态失败:', error)
      return false
    }
  }

  /**
   * 获取用户VIP状态
   * @param {string} openid - 用户openid
   * @returns {Promise<Object>} VIP状态信息
   */
  async getVipStatus(openid) {
    try {
      const result = await this.findByOpenid(openid)
      if (!result.success || !result.data) {
        return { status: false, expiredAt: null }
      }
      return result.data.vip || { status: false, expiredAt: null }
    } catch (error) {
      console.error('获取VIP状态失败:', error)
      return { status: false, expiredAt: null }
    }
  }

  /**
   * 更新用户VIP状态
   * @param {string} openid - 用户openid
   * @param {Object} vipInfo - VIP信息
   * @returns {Promise<Object>} 更新结果
   */
  async updateVipStatus(openid, vipInfo = {}) {
    const updateData = {
      'vip.status': vipInfo.status || false,
      'vip.expiredAt': vipInfo.expiredAt || null,
      updateTime: new Date()
    }
    return await this.updateByOpenid(openid, updateData)
  }

  /**
   * 更新用户签到统计
   * @param {string} openid - 用户openid
   * @param {Object} checkInStats - 签到统计数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateCheckInStats(openid, checkInStats) {
    try {
      return await this.updateByOpenid(openid, { checkInStats })
    } catch (error) {
      console.error('更新用户签到统计失败:', error)
      return {
        success: false,
        errMsg: error.message || '更新签到统计失败'
      }
    }
  }

  /**
   * 更新用户积分
   * @param {string} openid - 用户openid
   * @param {number} points - 新的积分余额
   * @returns {Promise<Object>} 更新结果
   */
  async updatePoints(openid, points) {
    try {
      return await this.updateByOpenid(openid, { points })
    } catch (error) {
      console.error('更新用户积分失败:', error)
      return {
        success: false,
        errMsg: error.message || '更新积分失败'
      }
    }
  }

  /**
   * 增加用户积分
   * @param {string} openid - 用户openid
   * @param {number} amount - 增加的积分数量
   * @returns {Promise<Object>} 更新结果
   */
  async addPoints(openid, amount) {
    try {
      const userResult = await this.findByOpenid(openid)
      if (!userResult.success || !userResult.data) {
        return {
          success: false,
          errMsg: '用户不存在'
        }
      }

      const currentPoints = userResult.data.points || 0
      const newPoints = currentPoints + amount

      return await this.updatePoints(openid, newPoints)
    } catch (error) {
      console.error('增加用户积分失败:', error)
      return {
        success: false,
        errMsg: error.message || '增加积分失败'
      }
    }
  }

  /**
   * 减少用户积分
   * @param {string} openid - 用户openid
   * @param {number} amount - 减少的积分数量
   * @returns {Promise<Object>} 更新结果
   */
  async spendPoints(openid, amount) {
    try {
      const userResult = await this.findByOpenid(openid)
      if (!userResult.success || !userResult.data) {
        return {
          success: false,
          errMsg: '用户不存在'
        }
      }

      const currentPoints = userResult.data.points || 0
      if (currentPoints < amount) {
        return {
          success: false,
          errMsg: '积分余额不足'
        }
      }

      const newPoints = currentPoints - amount
      return await this.updatePoints(openid, newPoints)
    } catch (error) {
      console.error('减少用户积分失败:', error)
      return {
        success: false,
        errMsg: error.message || '减少积分失败'
      }
    }
  }

  /**
   * 获取用户统计信息
   * @returns {Promise<Object>} 统计结果
   */
  async getUserStats() {
    try {
      const totalResult = await this.count()
      const vipResult = await this.count({ 'vip.status': true })

      if (!totalResult.success || !vipResult.success) {
        throw new Error('获取统计信息失败')
      }

      return {
        success: true,
        data: {
          total: totalResult.data.total,
          vipMembers: vipResult.data.total,
          freeMembers: totalResult.data.total - vipResult.data.total
        }
      }
    } catch (error) {
      console.error('获取用户统计失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取用户统计失败'
      }
    }
  }
}

module.exports = new UsersDB()
