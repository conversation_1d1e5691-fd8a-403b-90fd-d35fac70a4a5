/**
 * 数据库基础操作封装
 */

const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

/**
 * 基础数据库操作类
 */
class BaseDB {
  constructor(collectionName, options = {}) {
    this.db = db
    this.collection = db.collection(collectionName)
    this.collectionName = collectionName

    // 控制是否自动维护 createTime 和 updateTime 字段
    this.autoTimestamp = options.autoTimestamp !== false // 默认为 true
  }

  /**
   * 创建文档
   * @param {Object} data - 要创建的数据
   * @returns {Promise<Object>} 创建结果
   */
  async create(data) {
    try {
      const documentData = { ...data }

      if (this.autoTimestamp) {
        documentData.createTime = new Date()
        documentData.updateTime = new Date()
      }

      const result = await this.collection.add({
        data: documentData
      })

      return {
        success: true,
        data: { _id: result._id, ...data }
      }
    } catch (error) {
      console.error(`创建${this.collectionName}文档失败:`, error)
      return {
        success: false,
        errMsg: error.message || `创建${this.collectionName}失败`
      }
    }
  }

  /**
   * 根据ID查询文档
   * @param {string} id - 文档ID
   * @returns {Promise<Object>} 查询结果
   */
  async findById(id) {
    try {
      const result = await this.collection.doc(id).get()
      
      return {
        success: true,
        data: result.data
      }
    } catch (error) {
      console.error(`查询${this.collectionName}文档失败:`, error)
      return {
        success: false,
        errMsg: error.message || `查询${this.collectionName}失败`
      }
    }
  }

  /**
   * 条件查询文档
   * @param {Object} where - 查询条件
   * @param {Object} options - 查询选项 {limit, skip, orderBy}
   * @returns {Promise<Object>} 查询结果
   */
  async find(where = {}, options = {}) {
    try {
      let query = this.collection.where(where)
      
      // 排序
      if (options.orderBy) {
        query = query.orderBy(options.orderBy.field, options.orderBy.order || 'asc')
      }
      
      // 分页
      if (options.skip) {
        query = query.skip(options.skip)
      }
      
      if (options.limit) {
        query = query.limit(options.limit)
      }
      
      const result = await query.get()
      
      return {
        success: true,
        data: result.data,
        total: result.data.length
      }
    } catch (error) {
      console.error(`查询${this.collectionName}列表失败:`, error)
      return {
        success: false,
        errMsg: error.message || `查询${this.collectionName}列表失败`
      }
    }
  }

  /**
   * 查询单个文档
   * @param {Object} where - 查询条件
   * @returns {Promise<Object>} 查询结果
   */
  async findOne(where) {
    try {
      const result = await this.collection.where(where).limit(1).get()
      
      return {
        success: true,
        data: result.data.length > 0 ? result.data[0] : null
      }
    } catch (error) {
      console.error(`查询${this.collectionName}文档失败:`, error)
      return {
        success: false,
        errMsg: error.message || `查询${this.collectionName}失败`
      }
    }
  }

  /**
   * 更新文档
   * @param {string} id - 文档ID
   * @param {Object} data - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateById(id, data) {
    try {
      const updateData = { ...data }

      if (this.autoTimestamp) {
        updateData.updateTime = new Date()
      }

      const result = await this.collection.doc(id).update({
        data: updateData
      })

      return {
        success: true,
        data: { updated: result.stats.updated }
      }
    } catch (error) {
      console.error(`更新${this.collectionName}文档失败:`, error)
      return {
        success: false,
        errMsg: error.message || `更新${this.collectionName}失败`
      }
    }
  }

  /**
   * 条件更新文档
   * @param {Object} where - 查询条件
   * @param {Object} data - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async update(where, data) {
    try {
      const updateData = { ...data }

      if (this.autoTimestamp) {
        updateData.updateTime = new Date()
      }

      const result = await this.collection.where(where).update({
        data: updateData
      })

      return {
        success: true,
        data: { updated: result.stats.updated }
      }
    } catch (error) {
      console.error(`更新${this.collectionName}文档失败:`, error)
      return {
        success: false,
        errMsg: error.message || `更新${this.collectionName}失败`
      }
    }
  }

  /**
   * 删除文档
   * @param {string} id - 文档ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteById(id) {
    try {
      const result = await this.collection.doc(id).remove()
      
      return {
        success: true,
        data: { deleted: result.stats.removed }
      }
    } catch (error) {
      console.error(`删除${this.collectionName}文档失败:`, error)
      return {
        success: false,
        errMsg: error.message || `删除${this.collectionName}失败`
      }
    }
  }

  /**
   * 条件删除文档
   * @param {Object} where - 查询条件
   * @returns {Promise<Object>} 删除结果
   */
  async delete(where) {
    try {
      const result = await this.collection.where(where).remove()
      
      return {
        success: true,
        data: { deleted: result.stats.removed }
      }
    } catch (error) {
      console.error(`删除${this.collectionName}文档失败:`, error)
      return {
        success: false,
        errMsg: error.message || `删除${this.collectionName}失败`
      }
    }
  }

  /**
   * 统计文档数量
   * @param {Object} where - 查询条件
   * @returns {Promise<Object>} 统计结果
   */
  async count(where = {}) {
    try {
      const result = await this.collection.where(where).count()
      
      return {
        success: true,
        data: { total: result.total }
      }
    } catch (error) {
      console.error(`统计${this.collectionName}文档失败:`, error)
      return {
        success: false,
        errMsg: error.message || `统计${this.collectionName}失败`
      }
    }
  }

  /**
   * 检查集合是否存在，不存在则创建
   * @returns {Promise<Object>} 操作结果
   */
  async ensureCollection() {
    try {
      // 尝试获取集合信息
      await this.collection.get()
      return { success: true, message: `集合${this.collectionName}已存在` }
    } catch (error) {
      // 集合不存在，创建集合
      try {
        await this.collection.add({ data: { _temp: true } })
        await this.collection.where({ _temp: true }).remove()
        return { success: true, message: `集合${this.collectionName}创建成功` }
      } catch (createError) {
        console.error(`创建集合${this.collectionName}失败:`, createError)
        return {
          success: false,
          errMsg: createError.message || `创建集合${this.collectionName}失败`
        }
      }
    }
  }
}

module.exports = BaseDB
