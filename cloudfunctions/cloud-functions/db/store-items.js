/**
 * 商店商品数据库操作
 */

const BaseDB = require('./base')

class StoreItemsDB extends BaseDB {
  constructor() {
    super('store_items')
  }

  /**
   * 创建商品
   * @param {Object} itemData - 商品数据
   * @returns {Promise<Object>} 操作结果
   */
  async createItem(itemData) {
    const defaultData = {
      id: itemData.id, // 商品唯一标识
      name: itemData.name,
      description: itemData.description,
      type: itemData.type || 'vip_days',
      value: itemData.value, // 商品价值（如VIP天数）
      pointsCost: itemData.pointsCost, // 积分成本
      icon: itemData.icon || '🎁',
      popular: itemData.popular || false,
      enabled: itemData.enabled !== false, // 默认启用
      published: itemData.published || false, // 是否发布给正式用户
      sortOrder: itemData.sortOrder || 0, // 排序权重
      tags: itemData.tags || [], // 商品标签
      metadata: itemData.metadata || {}, // 扩展元数据
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...itemData
    }

    return await this.create(defaultData)
  }

  /**
   * 获取用户可见的商品列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 商品列表
   */
  async getAvailableItems(options = {}) {
    try {
      const {
        isTestUser = false, // 是否为测试用户
        type = null,
        tags = null,
        limit = 50,
        skip = 0
      } = options

      let query = { enabled: true }

      // 测试用户可以看到所有启用商品，正式用户只能看到已发布的商品
      if (!isTestUser) {
        query.published = true
      }

      // 类型筛选
      if (type) {
        query.type = type
      }

      // 标签筛选
      if (tags && tags.length > 0) {
        query.tags = this.db.command.in(tags)
      }

      const result = await this.collection
        .where(query)
        .orderBy('sortOrder', 'asc')
        .orderBy('createdAt', 'desc')
        .skip(skip)
        .limit(limit)
        .get()

      return {
        success: true,
        data: result.data || []
      }
    } catch (error) {
      console.error('获取可用商品失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取商品失败'
      }
    }
  }

  /**
   * 获取所有商品（管理用）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 商品列表
   */
  async getAllItems(options = {}) {
    try {
      const {
        enabled = null, // null=全部, true=启用, false=禁用
        type = null,
        limit = 50,
        skip = 0
      } = options

      let query = {}

      if (enabled !== null) {
        query.enabled = enabled
      }

      if (type) {
        query.type = type
      }

      const result = await this.collection
        .where(query)
        .orderBy('sortOrder', 'asc')
        .orderBy('createdAt', 'desc')
        .skip(skip)
        .limit(limit)
        .get()

      return {
        success: true,
        data: result.data || []
      }
    } catch (error) {
      console.error('获取所有商品失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取商品失败'
      }
    }
  }

  /**
   * 根据ID获取商品
   * @param {string} itemId - 商品ID
   * @returns {Promise<Object>} 商品信息
   */
  async getItemById(itemId) {
    try {
      const result = await this.findOne({ id: itemId })
      return result
    } catch (error) {
      console.error('获取商品详情失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取商品详情失败'
      }
    }
  }

  /**
   * 更新商品
   * @param {string} itemId - 商品ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateItem(itemId, updateData) {
    try {
      const updatedData = {
        ...updateData,
        updatedAt: new Date().toISOString()
      }

      const result = await this.collection
        .where({ id: itemId })
        .update({
          data: updatedData
        })

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('更新商品失败:', error)
      return {
        success: false,
        errMsg: error.message || '更新商品失败'
      }
    }
  }

  /**
   * 启用/禁用商品
   * @param {string} itemId - 商品ID
   * @param {boolean} enabled - 是否启用
   * @returns {Promise<Object>} 操作结果
   */
  async toggleItemStatus(itemId, enabled) {
    try {
      return await this.updateItem(itemId, { enabled })
    } catch (error) {
      console.error('切换商品状态失败:', error)
      return {
        success: false,
        errMsg: error.message || '切换商品状态失败'
      }
    }
  }

  /**
   * 删除商品
   * @param {string} itemId - 商品ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteItem(itemId) {
    try {
      const result = await this.collection
        .where({ id: itemId })
        .remove()

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('删除商品失败:', error)
      return {
        success: false,
        errMsg: error.message || '删除商品失败'
      }
    }
  }

  /**
   * 批量更新商品排序
   * @param {Array} sortData - 排序数据 [{id, sortOrder}]
   * @returns {Promise<Object>} 更新结果
   */
  async updateSortOrder(sortData) {
    try {
      const updatePromises = sortData.map(item => 
        this.updateItem(item.id, { sortOrder: item.sortOrder })
      )

      await Promise.all(updatePromises)

      return {
        success: true,
        data: { updated: sortData.length }
      }
    } catch (error) {
      console.error('更新商品排序失败:', error)
      return {
        success: false,
        errMsg: error.message || '更新排序失败'
      }
    }
  }

  /**
   * 获取商品统计
   * @returns {Promise<Object>} 统计结果
   */
  async getItemStats() {
    try {
      const allResult = await this.count()
      const enabledResult = await this.count({ enabled: true })
      const disabledResult = await this.count({ enabled: false })

      return {
        success: true,
        data: {
          total: allResult.success ? allResult.data.total : 0,
          enabled: enabledResult.success ? enabledResult.data.total : 0,
          disabled: disabledResult.success ? disabledResult.data.total : 0
        }
      }
    } catch (error) {
      console.error('获取商品统计失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取统计失败'
      }
    }
  }

  /**
   * 初始化默认商品数据
   * @returns {Promise<Object>} 初始化结果
   */
  async initializeDefaultItems() {
    try {
      // 检查是否已有商品
      const existingResult = await this.count()
      if (existingResult.success && existingResult.data.total > 0) {
        return {
          success: true,
          message: '商品已存在，跳过初始化'
        }
      }

      // 默认商品配置
      const defaultItems = [
        {
          id: 'vip_7_days',
          name: '7天VIP会员',
          description: '享受7天VIP特权，签到奖励翻倍',
          type: 'vip_days',
          value: 7,
          pointsCost: 50,
          icon: '👑',
          popular: false,
          published: true, // 已发布给正式用户
          sortOrder: 1,
          tags: ['vip', 'short_term']
        },
        {
          id: 'vip_15_days',
          name: '15天VIP会员',
          description: '享受15天VIP特权，签到奖励翻倍',
          type: 'vip_days',
          value: 15,
          pointsCost: 100,
          icon: '💎',
          popular: true,
          published: true, // 已发布给正式用户
          sortOrder: 2,
          tags: ['vip', 'popular']
        },
        {
          id: 'vip_30_days',
          name: '30天VIP会员',
          description: '享受30天VIP特权，签到奖励翻倍',
          type: 'vip_days',
          value: 30,
          pointsCost: 180,
          icon: '🌟',
          popular: false,
          published: false, // 仅测试用户可见
          sortOrder: 3,
          tags: ['vip', 'monthly']
        },
        {
          id: 'vip_90_days',
          name: '90天VIP会员',
          description: '享受90天VIP特权，签到奖励翻倍',
          type: 'vip_days',
          value: 90,
          pointsCost: 500,
          icon: '🏆',
          popular: false,
          published: false, // 仅测试用户可见
          sortOrder: 4,
          tags: ['vip', 'long_term']
        }
      ]

      // 批量创建商品
      const createPromises = defaultItems.map(item => this.createItem(item))
      await Promise.all(createPromises)

      return {
        success: true,
        data: { created: defaultItems.length }
      }
    } catch (error) {
      console.error('初始化默认商品失败:', error)
      return {
        success: false,
        errMsg: error.message || '初始化失败'
      }
    }
  }
}

module.exports = new StoreItemsDB()
