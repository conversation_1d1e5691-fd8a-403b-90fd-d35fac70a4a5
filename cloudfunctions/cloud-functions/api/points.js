/**
 * 积分相关API
 */

const pointsRecordsDB = require('../db/points-records')
const usersDB = require('../db/users')
const { getCurrentUser } = require('../utils/auth')
const { success, error, wrapAsync } = require('../utils/response')

/**
 * 获取积分余额
 */
exports.getPointsBalance = wrapAsync(async (params = {}) => {
  try {
    // 获取当前用户
    const userResult = await getCurrentUser()
    if (!userResult.success) {
      return error(userResult.errMsg || '获取用户信息失败')
    }

    const user = userResult.data
    const currentPoints = user.points || 0

    return success({
      points: currentPoints
    }, '获取积分余额成功')

  } catch (error) {
    console.error('获取积分余额失败:', error)
    return error(error.message || '获取积分余额失败')
  }
})

/**
 * 获取积分记录
 */
exports.getPointsRecords = wrapAsync(async (params = {}) => {
  try {
    const { type, limit = 20, skip = 0, startDate, endDate } = params

    // 获取当前用户
    const userResult = await getCurrentUser()
    if (!userResult.success) {
      return error(userResult.errMsg || '获取用户信息失败')
    }

    const user = userResult.data

    // 获取积分记录
    const recordsResult = await pointsRecordsDB.getUserRecords(user._id, {
      type,
      limit,
      skip,
      startDate,
      endDate
    })

    if (!recordsResult.success) {
      return error('获取积分记录失败')
    }

    // 获取积分统计
    const statsResult = await pointsRecordsDB.getUserPointsStats(user._id)

    return success({
      records: recordsResult.data,
      stats: statsResult.success ? statsResult.data : null,
      currentPoints: user.points || 0
    }, '获取积分记录成功')

  } catch (error) {
    console.error('获取积分记录失败:', error)
    return error(error.message || '获取积分记录失败')
  }
})

/**
 * 获取积分统计
 */
exports.getPointsStats = wrapAsync(async (params = {}) => {
  try {
    // 获取当前用户
    const userResult = await getCurrentUser()
    if (!userResult.success) {
      return error(userResult.errMsg || '获取用户信息失败')
    }

    const user = userResult.data

    // 获取积分统计
    const statsResult = await pointsRecordsDB.getUserPointsStats(user._id)
    if (!statsResult.success) {
      return error('获取积分统计失败')
    }

    // 获取来源统计
    const sourceStatsResult = await pointsRecordsDB.getPointsSourceStats(user._id)

    // 获取最近记录
    const recentRecordsResult = await pointsRecordsDB.getRecentRecords(user._id, 5)

    return success({
      stats: statsResult.data,
      sourceStats: sourceStatsResult.success ? sourceStatsResult.data : {},
      recentRecords: recentRecordsResult.success ? recentRecordsResult.data : [],
      currentPoints: user.points || 0
    }, '获取积分统计成功')

  } catch (error) {
    console.error('获取积分统计失败:', error)
    return error(error.message || '获取积分统计失败')
  }
})

/**
 * 内部方法：添加积分记录
 * @param {string} userId - 用户ID
 * @param {string} userNumber - 用户编号
 * @param {string} type - 类型 ('earn' | 'spend')
 * @param {number} amount - 积分数量
 * @param {string} source - 来源
 * @param {string} description - 描述
 * @param {string} relatedId - 关联ID
 * @returns {Promise<Object>} 操作结果
 */
async function addPointsRecord(userId, userNumber, type, amount, source, description, relatedId = null) {
  try {
    const recordData = {
      userId,
      userNumber,
      type,
      amount: type === 'spend' ? -Math.abs(amount) : Math.abs(amount),
      source,
      description,
      relatedId
    }

    return await pointsRecordsDB.createRecord(recordData)
  } catch (error) {
    console.error('添加积分记录失败:', error)
    return {
      success: false,
      errMsg: error.message || '添加积分记录失败'
    }
  }
}

/**
 * 内部方法：给用户增加积分
 * @param {string} openid - 用户openid
 * @param {number} amount - 积分数量
 * @param {string} source - 来源
 * @param {string} description - 描述
 * @param {string} relatedId - 关联ID
 * @returns {Promise<Object>} 操作结果
 */
async function earnPoints(openid, amount, source, description, relatedId = null) {
  try {
    // 获取用户信息
    const userResult = await usersDB.findByOpenid(openid)
    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        errMsg: '用户不存在'
      }
    }

    const user = userResult.data

    // 增加积分
    const updateResult = await usersDB.addPoints(openid, amount)
    if (!updateResult.success) {
      return updateResult
    }

    // 添加积分记录
    const recordResult = await addPointsRecord(
      user._id,
      user.userNumber || user.no,
      'earn',
      amount,
      source,
      description,
      relatedId
    )

    return {
      success: true,
      data: {
        amount,
        newBalance: (user.points || 0) + amount,
        record: recordResult.data
      }
    }
  } catch (error) {
    console.error('增加积分失败:', error)
    return {
      success: false,
      errMsg: error.message || '增加积分失败'
    }
  }
}

/**
 * 内部方法：用户消费积分
 * @param {string} openid - 用户openid
 * @param {number} amount - 积分数量
 * @param {string} source - 来源
 * @param {string} description - 描述
 * @param {string} relatedId - 关联ID
 * @returns {Promise<Object>} 操作结果
 */
async function spendPoints(openid, amount, source, description, relatedId = null) {
  try {
    // 获取用户信息
    const userResult = await usersDB.findByOpenid(openid)
    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        errMsg: '用户不存在'
      }
    }

    const user = userResult.data

    // 检查积分余额
    if ((user.points || 0) < amount) {
      return {
        success: false,
        errMsg: '积分余额不足'
      }
    }

    // 减少积分
    const updateResult = await usersDB.spendPoints(openid, amount)
    if (!updateResult.success) {
      return updateResult
    }

    // 添加积分记录
    const recordResult = await addPointsRecord(
      user._id,
      user.userNumber || user.no,
      'spend',
      amount,
      source,
      description,
      relatedId
    )

    return {
      success: true,
      data: {
        amount,
        newBalance: (user.points || 0) - amount,
        record: recordResult.data
      }
    }
  } catch (error) {
    console.error('消费积分失败:', error)
    return {
      success: false,
      errMsg: error.message || '消费积分失败'
    }
  }
}

// 导出内部方法供其他模块使用
exports.earnPoints = earnPoints
exports.spendPoints = spendPoints
exports.addPointsRecord = addPointsRecord
