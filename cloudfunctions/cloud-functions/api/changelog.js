/**
 * 更新日志相关API
 */

const changelogDB = require('../db/changelog')
const { success, error, wrapAsync } = require('../utils/response')

/**
 * 获取更新日志列表
 */
exports.getChangelogList = wrapAsync(async (params = {}) => {
  const result = await changelogDB.getPublishedLogs({
    limit: params.limit || 20,
    skip: params.skip || 0
  })

  if (!result.success) {
    return result
  }

  return success(result.data, '获取更新日志成功')
})

/**
 * 创建更新日志集合（用于初始化）
 */
exports.createChangelogCollection = wrapAsync(async (params = {}) => {
  // 确保集合存在
  const ensureResult = await changelogDB.ensureCollection()

  if (!ensureResult.success) {
    return ensureResult
  }

  // 初始化默认日志
  const initResult = await changelogDB.initDefaultLogs()

  if (!initResult.success) {
    return initResult
  }

  return success(null, '更新日志集合初始化成功')
})