/**
 * 积分商店相关API
 */

const redemptionCodesDB = require('../db/redemption-codes')
const storeItemsDB = require('../db/store-items')
const { getCurrentUser, incrementApiCallCount } = require('../utils/auth')
const { success, error, wrapAsync } = require('../utils/response')
const { spendPoints } = require('./points')



/**
 * 测试数据库连接
 */
exports.testDatabase = wrapAsync(async (params = {}) => {
  try {
    console.log('测试数据库连接...')

    // 直接创建 BaseDB 实例进行测试
    const BaseDB = require('../db/base')
    const testDB = new BaseDB('store_items')

    console.log('BaseDB 实例:', typeof testDB)
    console.log('testDB.collection:', !!testDB.collection)
    console.log('testDB.collectionName:', testDB.collectionName)
    console.log('testDB.db:', !!testDB.db)

    // 尝试使用 BaseDB 实例查询
    const baseResult = await testDB.collection.limit(1).get()
    console.log('BaseDB 查询结果:', baseResult)

    // 测试 storeItemsDB 实例
    console.log('storeItemsDB:', typeof storeItemsDB)
    console.log('storeItemsDB.collection:', !!storeItemsDB.collection)
    console.log('storeItemsDB.collectionName:', storeItemsDB.collectionName)
    console.log('storeItemsDB.db:', !!storeItemsDB.db)

    // 尝试使用 storeItemsDB
    const storeResult = await storeItemsDB.collection.limit(1).get()
    console.log('storeItemsDB 查询结果:', storeResult)

    return success({
      baseQuery: baseResult,
      storeQuery: storeResult,
      collectionName: storeItemsDB.collectionName
    }, '数据库连接测试成功')
  } catch (error) {
    console.error('数据库连接测试失败:', error)
    return error('数据库连接测试失败: ' + error.message)
  }
})

/**
 * 获取商店商品列表
 */
exports.getStoreItems = wrapAsync(async (params = {}) => {
  try {
    // 获取当前用户
    const userResult = await getCurrentUser()
    if (!userResult.success) {
      return error(userResult.errMsg || '获取用户信息失败')
    }

    const user = userResult.data
    const currentPoints = user.points || 0

    // 检查用户是否为测试用户
    const isTestUser = user.isTestUser || false

    // 从数据库获取用户可见的商品
    const itemsResult = await storeItemsDB.getAvailableItems({
      isTestUser: isTestUser,
      type: params.type,
      tags: params.tags,
      limit: params.limit || 50
    })

    if (!itemsResult.success) {
      return error('获取商品列表失败')
    }

    // 添加用户是否能够购买的标识
    const itemsWithAffordable = itemsResult.data.map(item => ({
      ...item,
      affordable: currentPoints >= item.pointsCost
    }))

    return success({
      items: itemsWithAffordable,
      currentPoints,
      isTestUser: isTestUser
    }, '获取商店商品成功')

  } catch (error) {
    console.error('获取商店商品失败:', error)
    return error(error.message || '获取商店商品失败')
  }
})

/**
 * 购买商品
 */
exports.purchaseItem = wrapAsync(async (params = {}) => {
  try {
    // 增加API调用计数
    await incrementApiCallCount()

    console.log('purchaseItem 接收到的参数:', JSON.stringify(params))
    console.log('参数类型:', typeof params)
    console.log('参数键:', Object.keys(params))

    const { itemId } = params
    console.log('提取的 itemId:', itemId)

    if (!itemId) {
      console.log('itemId 为空，返回错误')
      return error('请提供商品ID')
    }

    // 获取当前用户
    const userResult = await getCurrentUser()
    if (!userResult.success) {
      return error(userResult.errMsg || '获取用户信息失败')
    }

    const user = userResult.data

    // 从数据库获取商品信息
    const itemResult = await storeItemsDB.getItemById(itemId)
    if (!itemResult.success || !itemResult.data) {
      return error('商品不存在')
    }

    const item = itemResult.data

    // 检查商品是否启用
    if (!item.enabled) {
      return error('商品已下架')
    }

    // 检查商品是否对当前用户可见
    if (!item.published && !user.isTestUser) {
      return error('商品暂未开放')
    }


    // 检查积分余额
    if ((user.points || 0) < item.pointsCost) {
      return error('积分余额不足')
    }

    // 消费积分
    const spendResult = await spendPoints(
      user.openid,
      item.pointsCost,
      'purchase',
      `购买${item.name}`,
      null
    )

    if (!spendResult.success) {
      return error(spendResult.errMsg || '积分扣除失败')
    }

    // 生成兑换码
    const codeResult = await redemptionCodesDB.generateCode({
      type: item.type,
      value: item.value,
      pointsCost: item.pointsCost,
      createdBy: user._id
    })

    if (!codeResult.success) {
      // 如果生成兑换码失败，需要退还积分
      // 这里简化处理，实际应该有事务机制
      console.error('生成兑换码失败，需要退还积分:', codeResult.errMsg)
      return error('购买失败，请重试')
    }

    return success({
      item,
      redemptionCode: codeResult.data,
      pointsSpent: item.pointsCost,
      newBalance: spendResult.data.newBalance
    }, '购买成功！')

  } catch (error) {
    console.error('购买商品失败:', error)
    return error(error.message || '购买失败')
  }
})

/**
 * 获取我的兑换码
 */
exports.getMyRedemptionCodes = wrapAsync(async (params = {}) => {
  try {
    const { status, limit = 20, skip = 0 } = params

    // 获取当前用户
    const userResult = await getCurrentUser()
    if (!userResult.success) {
      return error(userResult.errMsg || '获取用户信息失败')
    }

    const user = userResult.data

    // 获取用户购买的兑换码（通过createdBy字段查询）
    const codesResult = await redemptionCodesDB.getUserCreatedCodes(user._id, {
      status,
      limit,
      skip
    })

    if (!codesResult.success) {
      return error('获取兑换码失败')
    }

    // 获取统计信息
    const statsResult = await redemptionCodesDB.getCodeStats(user._id)

    return success({
      codes: codesResult.data,
      stats: statsResult.success ? statsResult.data : null
    }, '获取兑换码成功')

  } catch (error) {
    console.error('获取兑换码失败:', error)
    return error(error.message || '获取兑换码失败')
  }
})

/**
 * 使用兑换码
 */
exports.redeemCode = wrapAsync(async (params = {}) => {
  try {
    // 增加API调用计数
    await incrementApiCallCount()

    const { code } = params
    if (!code) {
      return error('请提供兑换码')
    }

    // 获取当前用户
    const userResult = await getCurrentUser()
    if (!userResult.success) {
      return error(userResult.errMsg || '获取用户信息失败')
    }

    const user = userResult.data

    // 验证并使用兑换码
    const useResult = await redemptionCodesDB.useCode(code, user._id)
    if (!useResult.success) {
      return error(useResult.errMsg || '兑换码使用失败')
    }

    const codeData = useResult.data

    // 根据兑换码类型执行相应操作
    if (codeData.type === 'vip_days') {
      // 添加VIP天数
      const vipResult = await addVipDays(user, codeData.value, code)
      if (!vipResult.success) {
        console.error('添加VIP天数失败:', vipResult.errMsg)
        return error('兑换失败，请联系客服')
      }

      return success({
        code: codeData,
        vipDays: codeData.value,
        newVipStatus: vipResult.data
      }, `成功兑换${codeData.value}天VIP会员！`)
    }

    return success({
      code: codeData
    }, '兑换成功！')

  } catch (error) {
    console.error('使用兑换码失败:', error)
    return error(error.message || '兑换失败')
  }
})

/**
 * 内部方法：添加VIP天数
 * @param {Object} user - 用户信息
 * @param {number} days - 天数
 * @param {string} code - 兑换码
 * @returns {Promise<Object>} 操作结果
 */
async function addVipDays(user, days, code) {
  try {
    const usersDB = require('../db/users')
    const vipRecordsDB = require('../db/vip-records')

    const now = new Date()
    let newExpiredAt

    if (user.vip?.status && user.vip?.expiredAt) {
      // 如果已经是VIP，在现有到期时间基础上延长
      const currentExpiredAt = new Date(user.vip.expiredAt)
      if (currentExpiredAt > now) {
        newExpiredAt = new Date(currentExpiredAt.getTime() + days * 24 * 60 * 60 * 1000)
      } else {
        newExpiredAt = new Date(now.getTime() + days * 24 * 60 * 60 * 1000)
      }
    } else {
      // 如果不是VIP，从现在开始计算
      newExpiredAt = new Date(now.getTime() + days * 24 * 60 * 60 * 1000)
    }

    // 更新用户VIP状态
    const updateResult = await usersDB.updateByOpenid(user.openid, {
      vip: {
        status: true,
        expiredAt: newExpiredAt.toISOString()
      }
    })

    if (!updateResult.success) {
      return updateResult
    }

    // 创建VIP记录
    await vipRecordsDB.createVipRecord({
      userId: user._id,
      type: 'redeem_code',
      days: days,
      description: `通过兑换码获得${days}天VIP`,
      source: code
    })

    return {
      success: true,
      data: {
        status: true,
        expiredAt: newExpiredAt.toISOString(),
        daysAdded: days
      }
    }
  } catch (error) {
    console.error('添加VIP天数失败:', error)
    return {
      success: false,
      errMsg: error.message || '添加VIP天数失败'
    }
  }
}

/**
 * 初始化商店商品（系统内部调用）
 */
exports.initializeStoreItems = wrapAsync(async (params = {}) => {
  try {
    const result = await storeItemsDB.initializeDefaultItems()

    if (result.success) {
      return success(result.data, result.message || '商品初始化成功')
    } else {
      return error(result.errMsg || '商品初始化失败')
    }
  } catch (error) {
    console.error('初始化商店商品失败:', error)
    return error(error.message || '初始化失败')
  }
})
